<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\UserSessionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Modules\Company\app\Models\Company;
use Tests\TestCase;

class UserCompanyChangeTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_sessions_are_cleared_when_company_changes()
    {
        // Create companies
        $company1 = Company::factory()->create();
        $company2 = Company::factory()->create();

        // Create user with company1
        $user = User::factory()->create([
            'company_id' => $company1->id
        ]);

        // Simulate active session
        DB::table('sessions')->insert([
            'id' => 'test_session_id',
            'user_id' => $user->id,
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'payload' => 'test_payload',
            'last_activity' => time()
        ]);

        // Verify session exists
        $this->assertDatabaseHas('sessions', [
            'user_id' => $user->id
        ]);

        // Change user's company
        $user->update(['company_id' => $company2->id]);

        // Handle company change
        UserSessionService::handleCompanyChange($user, $company1->id, $company2->id);

        // Verify sessions are cleared
        $this->assertDatabaseMissing('sessions', [
            'user_id' => $user->id
        ]);
    }

    public function test_session_validation_detects_inconsistency()
    {
        $company1 = Company::factory()->create();
        $company2 = Company::factory()->create();

        $user = User::factory()->create([
            'company_id' => $company1->id
        ]);

        // Simulate session with old company data
        session(['userTenancy' => (object) ['company_id' => $company2->id]]);

        // Validation should fail
        $this->assertFalse(UserSessionService::validateUserSession($user));

        // Update user to match session
        $user->company_id = $company2->id;

        // Validation should pass
        $this->assertTrue(UserSessionService::validateUserSession($user));
    }
}
