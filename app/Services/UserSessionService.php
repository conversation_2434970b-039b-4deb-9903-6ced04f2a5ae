<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserSessionService
{
    /**
     * Clear all sessions for a specific user
     * This is useful when user's company assignment changes
     */
    public static function clearUserSessions(int $userId): void
    {
        try {
            // Clear database sessions for the user
            DB::table('sessions')
                ->where('user_id', $userId)
                ->delete();

            // Clear any cached session data
            cache()->forget("user_tenancy_{$userId}");
            cache()->forget("userTenancy");
            
            Log::info("Cleared sessions for user ID: {$userId}");
        } catch (\Exception $e) {
            Log::error('Failed to clear user sessions: ' . $e->getMessage());
        }
    }

    /**
     * Clear sessions for multiple users
     */
    public static function clearMultipleUserSessions(array $userIds): void
    {
        try {
            // Clear database sessions for multiple users
            DB::table('sessions')
                ->whereIn('user_id', $userIds)
                ->delete();

            // Clear cached session data for each user
            foreach ($userIds as $userId) {
                cache()->forget("user_tenancy_{$userId}");
            }
            
            Log::info("Cleared sessions for user IDs: " . implode(', ', $userIds));
        } catch (\Exception $e) {
            Log::error('Failed to clear multiple user sessions: ' . $e->getMessage());
        }
    }

    /**
     * Validate if current session is consistent with user's database state
     */
    public static function validateUserSession(User $user): bool
    {
        $sessionUser = session('userTenancy');
        
        if (!$sessionUser) {
            return true; // No session to validate
        }

        // Check if company_id matches
        if (isset($sessionUser->company_id) && $sessionUser->company_id !== $user->company_id) {
            return false;
        }

        return true;
    }

    /**
     * Force logout current user if they are the target user
     */
    public static function logoutIfCurrentUser(int $targetUserId): bool
    {
        if (auth()->id() === $targetUserId) {
            auth()->logout();
            session()->flush();
            return true;
        }
        return false;
    }

    /**
     * Handle company change for a user
     * This method encapsulates all the necessary steps when a user's company changes
     */
    public static function handleCompanyChange(User $user, ?int $oldCompanyId, ?int $newCompanyId): void
    {
        if ($oldCompanyId === $newCompanyId) {
            return; // No change
        }

        // Clear the user's sessions
        self::clearUserSessions($user->id);

        // If it's the current user, logout immediately
        $isCurrentUser = self::logoutIfCurrentUser($user->id);

        Log::info("Company changed for user {$user->id} from {$oldCompanyId} to {$newCompanyId}. Current user logged out: " . ($isCurrentUser ? 'yes' : 'no'));
    }
}
