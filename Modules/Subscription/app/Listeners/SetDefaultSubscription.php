<?php

namespace Modules\Subscription\app\Listeners;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Subscription\Services\PlanService;
use Modules\Subscription\Services\SubscriptionService;

class SetDefaultSubscription
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event)
    {

        $planService = app(PlanService::class);
        $plan = $planService->getDefaultPlan();

        if (!empty($plan)) {
            $subscriptionService = app(SubscriptionService::class);
            $model = $subscriptionService->create(['plan_id'=>$plan->id,'account'=>$event->model]);
            $model->is_current=1;
            $model->status="active";
            $model->save();
        }
    }
}
