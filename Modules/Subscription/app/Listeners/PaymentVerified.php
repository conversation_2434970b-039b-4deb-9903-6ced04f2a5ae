<?php

namespace Modules\Subscription\app\Listeners;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Carbon;
use Modules\Payment\app\Events\PaymentVerifiedEvent;
use Modules\Subscription\app\Enums\SubscriptionStatusEnum;
use Modules\Subscription\app\models\Subscription;

class PaymentVerified
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(PaymentVerifiedEvent $event)
    {

        if ($event->responsePaymentDTO['updated_payment']->order_table == Subscription::class) {
            //todo status management like if upgraded ,canceled can't be all requested or active
            $model = $event->responsePaymentDTO['updated_payment']->subscription;

            //edit old subscription
            if (!empty($model->subscriber->activeSubscription)) {
                $model->subscriber->activeSubscription->status = SubscriptionStatusEnum::FINISHED;
                $model->subscriber->activeSubscription->is_current = 0;
                $model->subscriber->activeSubscription->canceled_at = Carbon::now();
                $model->subscriber->activeSubscription->save();
            }
            //edit new subscription
            $model->is_current = 1;
            $model->status = SubscriptionStatusEnum::ACTIVE;
            $model->save();

        }
    }
}
