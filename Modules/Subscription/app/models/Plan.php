<?php

namespace Modules\Subscription\app\models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Khaleds\Shared\Models\BaseModel;
use Modules\Subscription\app\Enums\SubscriptionIntervalEnum;
use App\Shared\HasRelationshipChecks;


/**
 * @property integer $id
 * @property integer $interval_id
 * @property mixed $name
 * @property mixed $description
 * @property int $order
 * @property float $price
 * @property float $discount_price
 * @property int $count
 * @property boolean $is_default
 * @property boolean $is_active
 * @property string $deleted_at
 * @property string $created_at
 * @property string $updated_at
 * @property Interval $interval
 * @property PlanFeature[] $planFeatures
 * @property Subscription[] $subscriptions
 */
class Plan extends BaseModel
{
    use HasRelationshipChecks;
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    protected $casts=[
        "is_default"=>"boolean",
        "interval"=> SubscriptionIntervalEnum::class

    ];
    /**
     * @var array
     */

    protected $guarded = ["id"];

    protected $relationsList = ["subscriptions"];

    /**
     * @return BelongsTo
     */
    public function interval()
    {
        return $this->belongsTo(Interval::class);
    }

    /**
     * @return HasMany
     */
    public function planFeatures()
    {
        return $this->hasMany(PlanFeature::class);
    }

    public function features(){
        return $this->belongsToMany(Feature::class,PlanFeature::class);
    }

    public function permissionFeatures()
    {
        return $this->belongsToMany(Feature::class,PlanFeature::class)
            ->whereNotNull("features.private_permission_id")
            ->withPivot('value');
    }
    /**
     * @return HasMany
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }
}
