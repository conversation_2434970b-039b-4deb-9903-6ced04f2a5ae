<?php

namespace Modules\Subscription\app\models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Khaleds\Shared\Models\BaseModel;

/**
 * @property integer $id
 * @property integer $plan_id
 * @property integer $feature_id
 * @property string $value
 * @property boolean $is_active
 * @property string $deleted_at
 * @property string $created_at
 * @property string $updated_at
 * @property Feature $feature
 * @property Plan $plan
 */
class PlanFeature extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'plan_feature';

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['plan_id', 'feature_id', 'value', 'is_active', 'deleted_at', 'created_at', 'updated_at'];

    /**
     * @return BelongsTo
     */
    public function feature()
    {
        return $this->belongsTo(Feature::class);
    }

    /**
     * @return BelongsTo
     */
    public function plan()
    {
        return $this->belongsTo(plan::class);
    }
}
