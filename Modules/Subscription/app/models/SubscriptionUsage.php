<?php

namespace Modules\Subscription\app\models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use <PERSON>haleds\Shared\Models\BaseModel;

/**
 * @property integer $id
 * @property integer $subscription_id
 * @property integer $feature_id
 * @property string $value
 * @property string $used
 * @property string $created_at
 * @property string $updated_at
 * @property Feature $feature
 * @property Subscription $subscription
 */
class SubscriptionUsage extends BaseModel
{
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['subscription_id', 'feature_id', 'value', 'used', 'created_at', 'updated_at'];

    /**
     * @return BelongsTo
     */
    public function feature()
    {
        return $this->belongsTo(Feature::class);
    }

    /**
     * @return BelongsTo
     */
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }
}

