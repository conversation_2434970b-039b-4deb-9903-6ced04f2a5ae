<?php

namespace Modules\Subscription\app\models;


use Illuminate\Database\Eloquent\Relations\HasMany;
use <PERSON><PERSON>ds\Shared\Models\BaseModel;
use Spatie\Permission\Models\Permission;

/**
 * @property integer $id
 * @property integer $permission_id
 * @property boolean $is_active
 * @property string $deleted_at
 * @property string $created_at
 * @property string $updated_at
 * @property Feature[] $features
 */
class PrivatePermission extends BaseModel
{
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['permission_id',  'deleted_at', 'created_at', 'updated_at', 'name'];

    /**
     * @return HasMany
     */
    public function features()
    {
        return $this->hasMany(Feature::class);
    }


    public function permission(){
        return $this->belongsTo(Permission::class);
    }


}

