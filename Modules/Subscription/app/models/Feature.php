<?php

namespace Modules\Subscription\app\models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Khaleds\Shared\Models\BaseModel;
use App\Shared\HasRelationshipChecks;


/**
 * @property integer $id
 * @property integer $private_permission_id
 * @property mixed $name
 * @property string $key
 * @property mixed $description
 * @property mixed $extra
 * @property boolean $is_active
 * @property string $deleted_at
 * @property string $created_at
 * @property string $updated_at
 * @property PrivatePermission $privatePermission
 * @property PlanFeature[] $planFeatures
 * @property SubscriptionUsage[] $subscriptionUsages
 */
class Feature extends BaseModel
{
    use HasRelationshipChecks;
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    protected $translatable=["name",'description'];
    protected $guarded=['id'];

    protected $relationsList = ["planFeatures"];

    /**
     * @var array
     */


    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'name' => 'json',
        'description' => 'json',
        'private_permission_id' => 'integer'
    ];

    /**
     * @return BelongsTo
     */
    public function private_permission()
    {
        return $this->belongsTo(PrivatePermission::class);
    }

    public function activePrivatePermission()
    {
        return $this->belongsTo(PrivatePermission::class,'private_permission_id','id');

    }

    /**
     * @return HasMany
     */
    public function planFeatures()
    {
        return $this->hasMany(PlanFeature::class);
    }

    /**
     * @return HasMany
     */
    public function subscriptionUsages()
    {
        return $this->hasMany(SubscriptionUsage::class);
    }
}
