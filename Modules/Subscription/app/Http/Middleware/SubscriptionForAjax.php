<?php

namespace Modules\Subscription\app\Http\Middleware;

use App\Enums\RoleEnum;
use Closure;
use Filament\Facades\Filament;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Modules\Subscription\app\models\PrivatePermission;
use Modules\Subscription\Helpers\MiddlewareHelper;

class SubscriptionForAjax
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {

        if (is_null(auth()->user()?->company))
            return $next($request);

        if ($request->route()->getName() == 'livewire.update') {
            $payload = json_decode(json_decode($request->getContent(), true)['components'][0]['snapshot'],true);
            $content = json_decode($request->getContent(), true);
            $updates = $content['components'][0]['updates'] ?? [];
            $calls = $content['components'][0]['calls'] ?? [];

            if ( $calls && ($calls[0]['method'] == "createAnother" || $calls[0]['method'] == "create") ){
                $originalPath = $payload['memo']['path'] ?? null;
                $originalMethod = $payload['memo']['method'] ?? null;
                $routeName = app('router')->getRoutes()->match(
                    Request::create($originalPath, $originalMethod)
                )->getName();

                $route = MiddlewareHelper::getShieldPermissionName($routeName);
                return MiddlewareHelper::checkPermission($request,$next,$route);
            }

        }
            return $next($request);
    }


}

/*

 * create seeder for all api permissions
 * cron job to delete un used subscriptions
 * private permissions
 * */
