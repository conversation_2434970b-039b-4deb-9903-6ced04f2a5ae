<?php

namespace Modules\Subscription\app\Console;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Modules\Subscription\app\Enums\SubscriptionStatusEnum;
use Modules\Subscription\app\models\Subscription;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;

class ExpirationCommand extends Command
{

    /**
     * The name and signature of the console command.
     */
    protected $signature = 'subscription:expiration';

    /**
     * The console command description.
     */
    protected $description = 'Command description.';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $subscriptions = Subscription::whereDate('expired_at',date('Y-m-d'))->orWhereDate('expired_at',Carbon::yesterday()->format('Y-m-d'))->whereNull('canceled_at')->get();

        foreach ($subscriptions as $subscription){
            $subscription->status = SubscriptionStatusEnum::FINISHED;
            $subscription->canceled_at = date(now());
            $subscription->save();
        }
    }


}
