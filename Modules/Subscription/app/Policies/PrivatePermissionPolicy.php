<?php

namespace Modules\Subscription\app\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Subscription\app\models\PrivatePermission;

class PrivatePermissionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_private::permission');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, PrivatePermission $privatePermission): bool
    {
        return $user->can('view_private::permission');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_private::permission');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, PrivatePermission $privatePermission): bool
    {
        return $user->can('update_private::permission');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, PrivatePermission $privatePermission): bool
    {
        return $user->can('delete_private::permission');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_private::permission');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, PrivatePermission $privatePermission): bool
    {
        return $user->can('force_delete_private::permission');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_private::permission');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, PrivatePermission $privatePermission): bool
    {
        return $user->can('restore_private::permission');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_private::permission');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, PrivatePermission $privatePermission): bool
    {
        return $user->can('replicate_private::permission');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_private::permission');
    }
}
