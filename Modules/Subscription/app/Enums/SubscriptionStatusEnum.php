<?php

namespace Modules\Subscription\app\Enums;

use Khaleds\Shared\Helpers\EnumToArray;

enum SubscriptionStatusEnum :string
{

    use EnumToArray;
    case NEW = 'new';
    case CANCELED = 'canceled';
    case FINISHED = 'finished';
    case ACTIVE = 'active';


    public function label(): string
    {
        return match($this) {
            self::NEW => 'New',
            self::CANCELED => 'Canceled',
            self::FINISHED => 'Finished',
            self::ACTIVE => 'Active',
        };
    }

    public static function trans(string $status): string
    {
        return match($status) {
            self::NEW->value => __('New'),
            self::CANCELED->value => __('Canceled'),
            self::FINISHED->value => __('Finished'),
            self::ACTIVE->value => __('Active'),
            default => $status,
        };
    }

    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }

    public function color(): string
    {
        return match($this) {
            self::NEW => 'gray',
            self::CANCELED => 'danger',
            self::FINISHED => 'warning',
            self::ACTIVE => 'success',
        };
    }

}
