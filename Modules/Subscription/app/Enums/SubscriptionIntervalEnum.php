<?php

namespace Modules\Subscription\app\Enums;

use Khaleds\Shared\Helpers\EnumToArray;

enum SubscriptionIntervalEnum: string
{
    use EnumToArray;

    case YEAR = 'year';
    case MONTH = 'month';
    case DAY = 'day';

    public function label(): string
    {
        return match($this) {
            self::YEAR => 'Yearly',
            self::MONTH => 'Monthly',
            self::DAY => 'Daily',
        };
    }

    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }

    public static function toArray(): array
    {
        return [
            self::MONTH->value => 'Monthly',
            self::YEAR->value => 'Yearly',
            self::DAY->value => 'Daily',
        ];
    }

    // Add this method to ensure proper value handling
    public static function fromValue(string $value): ?static
    {
        return match ($value) {
            'month' => self::MONTH,
            'year' => self::YEAR,
            'day' => self::DAY,
            default => null,
        };
    }

    public static function calculatins($interval,$count)
    {

        $startAt = now();
        return  match ($interval) {
                SubscriptionIntervalEnum::DAY => $startAt->addDays($count),
                SubscriptionIntervalEnum::YEAR => $startAt->addYears($count),
                default => $startAt->addMonths($count),
            };
    }
}
