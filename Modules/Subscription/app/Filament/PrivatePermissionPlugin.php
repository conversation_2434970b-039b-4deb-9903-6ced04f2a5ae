<?php

namespace Modules\Subscription\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Subscription\app\Filament\Resources\FeatureResource;
use Modules\Subscription\app\Filament\Resources\PrivatePermissionResource;
use Modules\Subscription\app\models\PrivatePermission;

class PrivatePermissionPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'PrivatePermission';
    }

    public function getId(): string
    {
        return 'PrivatePermission';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }
    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                PrivatePermissionResource::class,
            ]);
    }
}
