<?php

namespace Modules\Subscription\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Subscription\app\Filament\Resources\SubscriptionResource;

class SubscriptionPlugin implements plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Subscription';
    }

    public function getId(): string
    {
        return 'subscription';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }
    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                SubscriptionResource::class,
            ]);
    }
}
