<?php

namespace Modules\Subscription\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Account\app\Filament\Resources\AccountsResource;
use Modules\Subscription\app\Filament\Resources\FeatureResource;
use Modules\Subscription\app\Filament\Resources\PlanResource;

class PlanPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Feature';
    }

    public function getId(): string
    {
        return 'feature';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }
    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                PlanResource::class,
            ]);
    }
}
