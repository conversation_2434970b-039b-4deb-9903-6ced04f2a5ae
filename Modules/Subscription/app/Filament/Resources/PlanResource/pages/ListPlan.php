<?php

namespace Modules\Subscription\app\Filament\Resources\PlanResource\pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Subscription\app\Filament\Resources\FeatureResource;
use Modules\Subscription\app\Filament\Resources\PlanResource;

class ListPlan extends ListRecords
{
    protected static string $resource = PlanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
