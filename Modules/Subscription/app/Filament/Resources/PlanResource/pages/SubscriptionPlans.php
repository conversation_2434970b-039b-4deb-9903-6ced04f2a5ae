<?php
namespace Modules\Subscription\app\Filament\Resources\PlanResource\pages;

use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Modules\Payment\Classes\ClickPayPayment;
use Modules\Subscription\app\Enums\SubscriptionIntervalEnum;
use Modules\Subscription\app\Enums\SubscriptionStatusEnum;
use Modules\Subscription\app\Filament\Resources\PlanResource;
use Modules\Subscription\app\models\Plan;
use Modules\Subscription\app\models\Subscription;
use Filament\Notifications\Notification;
use Livewire\Component;
use Modules\Subscription\Services\SubscriptionService;

class SubscriptionPlans extends Page
{
    use HasPageShield;

    protected static string $view = 'filament.resources.plan-resource.pages.subscription-plans';
    protected static ?string $navigationIcon = 'heroicon-o-cube';
    protected static ?string $title = 'Subscription Plans';

    public $plans;
    public $currentSubscription;
    public $selectedInterval;
    public $showPaymentModal = false;
    public $paymentUrl = '';
    public $paymentResponse = null;

    public function mount()
    {
//        abort_unless($this->shouldRegisterNavigation(), 403);
//
//        if (!static::canAccess()) {
//            abort(403);
//        }
//        dd(auth()->user()->activeSubscription);
        $this->plans = Plan::where('is_active', true)
            ->orderBy('order')
            ->where('is_default',0)
            ->with(['planFeatures.feature'])
            ->get();

        // Set the default interval based on available plans
        $this->setDefaultInterval();

        $this->currentSubscription = Subscription::where('subscriber_id', auth()->user()->company?->user_id)
            ->where('subscriber_type', get_class(auth()->user()))
//            ->where('status', SubscriptionStatusEnum::ACTIVE)
            ->where('is_current', 1)
            ->whereNull('canceled_at')
            ->with(['plan.planFeatures.feature'])
            ->first();

        $this->dispatch('init-payment-listener');
    }

    private function setDefaultInterval()
    {
        // Check for monthly plans first
        if ($this->plans->contains('interval.value', 'month')) {
            $this->selectedInterval = 'month';
        }
        // If no monthly plans, check for yearly plans
        elseif ($this->plans->contains('interval.value', 'year')) {
            $this->selectedInterval = 'year';
        }
        // If no yearly plans, check for daily plans
        elseif ($this->plans->contains('interval.value', 'day')) {
            $this->selectedInterval = 'day';
        }
        // If no plans found, default to monthly
        else {
            $this->selectedInterval = 'month';
        }
    }
    public function getFilteredPlansProperty()
    {
        return $this->plans->filter(function ($plan) {
            return $plan->interval->value === $this->selectedInterval;
        });
    }

    public function subscribe($planId)
    {
        try {
            $data = [
                'plan_id' => $planId,
                'account' => auth()->user()
            ];

            $subscriptionService = app(SubscriptionService::class);
            $subscription = $subscriptionService->create($data);

            $payment = new ClickPayPayment();
            $payment = $payment->pay($subscription->price, null, $subscription->id, "", Subscription::class);

            $this->paymentUrl = $payment['redirect_url'];
            $this->showPaymentModal = true;

        } catch (\Exception $exception) {
            Notification::make()
                ->title('Error processing payment')
                ->danger()
                ->body($exception->getMessage())
                ->send();
        }
    }

    public function handlePaymentResponse($response)
    {
        $this->paymentResponse = $response;

        if ($response['success']) {
            $this->showPaymentModal = false;

            Notification::make()
                ->title('Payment Successful!')
                ->success()
                ->icon('heroicon-o-check-circle')
                ->duration(5000)
                ->send();

            $this->redirect(request()->header('Referer'));
        }
    }
    public static function getNavigationLabel(): string
    {
        return __("Subscription Plans");
    }
    public function getTitle(): string
    {
        return __('Subscription Plans');
    }


}
