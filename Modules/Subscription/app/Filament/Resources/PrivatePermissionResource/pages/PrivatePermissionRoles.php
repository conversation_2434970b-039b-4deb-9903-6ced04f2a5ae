<?php

namespace Modules\Subscription\app\Filament\Resources\PrivatePermissionResource\pages;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Facades\FilamentShield;
use <PERSON>zhanSalleh\FilamentShield\FilamentShieldPlugin;
use <PERSON><PERSON>hanSalleh\FilamentShield\Support\Utils;
use Filament\Actions\Action;
use Filament\Forms\Components\Component;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Filament\Support\Exceptions\Halt;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Modules\Subscription\app\Filament\Resources\PrivatePermissionResource;
use Filament\Forms;
use Modules\Subscription\app\models\PrivatePermission;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;


class PrivatePermissionRoles extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $resource = PrivatePermissionResource::class;

    protected static string $view = 'filament.resources.private-permission-resource.pages.private-permission-roles';
    protected static ?string $title = 'Private Permission Roles';
    protected static ?string $navigationLabel = 'Private Permission Roles';
    protected static ?string $breadcrumb = 'Private Permission Roles';

    public static function getResourcePermissionOptions(array $entity): array
    {
        $result = [];

        $permissions = Permission::whereIn('name', collect(Utils::getResourcePermissionPrefixes($entity['fqcn']))
            ->map(fn($permission) => $permission . '_' . $entity['resource']))
            ->get();

        foreach ($permissions as $permission) {
            $basePermission = str_replace('_' . $entity['resource'], '', $permission->name);
            $label = static::shield()->hasLocalizedPermissionLabels()
                ? FilamentShield::getLocalizedResourcePermissionLabel($basePermission)
                : $basePermission;

            $result[$permission->id] = $label;
        }

        return $result;
    }


    public static function shield(): FilamentShieldPlugin
    {
        return FilamentShieldPlugin::get();
    }

    public function getFormActions(): array
    {
        return [
            Action::make('save')->submit('save')
                ->label(__('save'))
        ];
    }

    public $data = [];

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Permissions')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('resources')
                            ->label(__('filament-shield::filament-shield.resources'))
                            ->schema([
                                Forms\Components\Grid::make()
                                    ->schema($this->getResourceEntitiesSchema())
                                    ->columns(3)
                            ])
                    ])
            ]);
    }

    protected function getResourceEntitiesSchema(): array
    {
        return collect(FilamentShield::getResources())
            ->sortKeys()
            ->map(function ($entity) {
                return Forms\Components\Section::make($entity['resource'])
                    ->schema([
                        Forms\Components\CheckboxList::make("data.{$entity['resource']}")  // Changed the name to include data prefix
                        ->label('')
                            ->options(function () use ($entity) {
                                return static::getResourcePermissionOptions($entity);
                            })
                            ->bulkToggleable()
                            ->columns(4)
                            ->live()
                    ]);
            })
            ->toArray();
    }

    public function save()
    {
        try {
            DB::beginTransaction();

            // Get all selected permission IDs from the form
            $selectedPermissionIds = collect($this->data)
                ->flatMap(fn($permissions) => !empty($permissions) ? $permissions : [])
                ->unique()
                ->values()
                ->toArray();

            // Bulk delete permissions not in selected list
            PrivatePermission::whereNotIn('permission_id', $selectedPermissionIds)
                ->delete();

            // Prepare bulk insert data
            $existingPermissions = PrivatePermission::whereIn('permission_id', $selectedPermissionIds)
                ->pluck('permission_id')
                ->toArray();

            $newPermissionIds = array_diff($selectedPermissionIds, $existingPermissions);

            if (!empty($newPermissionIds)) {
                $now = now();
                $insertData = array_map(function($permissionId) use ($now) {
                    return [
                        'permission_id' => $permissionId,
                        'name' => null,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }, $newPermissionIds);

                // Bulk insert new permissions
                PrivatePermission::insert($insertData);
            }

            DB::commit();

            Notification::make()
                ->success()
                ->title('Permissions saved successfully')
                ->send();

            return redirect()->back();

        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error saving permissions')
                ->message($e->getMessage())
                ->send();

            return redirect()->back();
        }
    }
    public function mount()
    {
        // Get existing permission IDs
        $existingPermissionIds = DB::table('private_permissions')
            ->whereNull('deleted_at')
            ->pluck('permission_id')
            ->map(fn($id) => (string) $id)
            ->toArray();

        // Initialize data array
        $this->data = [];

        // Get all resources
        $resources = FilamentShield::getResources();

        // For each resource, use the exact resource name as it appears in the form
        foreach ($resources as $resource) {
            // Use the original resource name without transformation
            $resourceName = $resource['resource'];

            // Get permissions for this resource
            $permissions = \Spatie\Permission\Models\Permission::where('name', 'like', "%_{$resourceName}")
                ->whereIn('id', $existingPermissionIds)
                ->pluck('id')
                ->map(fn($id) => (string) $id)
                ->toArray();

            if (!empty($permissions)) {
                $this->data[$resourceName] = $permissions;
            }
        }

        Log::info('Final Data Structure:', ['data' => $this->data]);

        // Fill the form with the data
        $this->form->fill([
            'data' => $this->data
        ]);
    }

    // Override the validate method to bypass validation
    public function validate($rules = null, $messages = [], $attributes = []): array
    {
        return $this->data;
    }

    public static function getNavigationGroup(): string
    {
        return  __('Subscription Management');
    }


    public function getBreadcrumb(): string
    {
        return __('Private Permission Roles');
    }

    public function getTitle(): string
    {
        return __('Private Permission Roles');
    }

    public static function getModelLabel(): string
    {
        return __('Private Permission Roles');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Private Permission Roles');
    }
}
