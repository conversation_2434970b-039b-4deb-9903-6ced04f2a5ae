<?php

namespace Modules\Subscription\app\Filament\Resources;

use Filament\Resources\Resource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Subscription\app\Filament\Resources\FeatureResource\pages\CreateFeature;
use Modules\Subscription\app\Filament\Resources\FeatureResource\pages\EditFeature;
use Modules\Subscription\app\Filament\Resources\FeatureResource\pages\ListFeatures;
use Modules\Subscription\app\models\Feature;
use Modules\Subscription\app\models\PrivatePermission;
use Filament\Forms\Components\Tabs;

class FeatureResource extends Resource
{
    protected static ?string $model = Feature::class;


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\Select::make('private_permission_id')
                            ->label(__('Private Permission'))
                            ->placeholder(__('Select a permission'))
                            ->options(function () {
                                return PrivatePermission::query()
                                    ->with('permission')
                                    ->get()
                                    ->pluck('permission.name', 'id');
                            })
                            ->searchable()
                            ->required(),

                        Tabs::make('Translations')
                            ->tabs([
                                Tabs\Tab::make('English')
                                    ->icon('heroicon-o-globe-alt')
                                    ->schema([
                                        Forms\Components\TextInput::make('name.en')
                                            ->label(__('Name'))
                                            ->placeholder(__('Enter name in English'))
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\Textarea::make('description.en')
                                            ->label(__('Description'))
                                            ->placeholder(__('Enter description in English'))
                                            ->maxLength(65535),
                                    ]),
                                Tabs\Tab::make('Arabic')
                                    ->icon('heroicon-o-globe-alt')
                                    ->schema([
                                        Forms\Components\TextInput::make('name.ar')
                                            ->label(__('Name'))
                                            ->placeholder(__('Enter name in Arabic'))
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\Textarea::make('description.ar')
                                            ->label(__('Description'))
                                            ->placeholder(__('Enter description in Arabic'))
                                            ->maxLength(65535),
                                    ]),
                            ])->columnSpanFull(),


                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('private_permission.permission.name')
                    ->label(__('Permission'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('description')
                    ->label(__('Description'))
                    ->searchable()
                    ->sortable()
                    ->wrap(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('private_permission_id')
                    ->label(__('Private Permission'))
                    ->placeholder(__('Select a permission'))
                    ->options(function () {
                        return PrivatePermission::query()
                            ->with('permission')
                            ->get()
                            ->pluck('permission.name', 'id');
                    })
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListFeatures::route('/'),
            'create' => CreateFeature::route('/create'),
            'edit' => EditFeature::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): string
    {
        return  __('Subscription Management');
    }

    public static function getNavigationLabel(): string
    {
        return __("Features");
    }
    public static function getBreadcrumb(): string
    {
        return __("Features");
    }


    public static function getModelLabel(): string
    {
        return __('Features');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Features');
    }
}
