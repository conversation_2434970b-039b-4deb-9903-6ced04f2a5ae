<?php

namespace Modules\Subscription\app\Filament\Resources;

use App\Filament\Resources\UserResource\Pages\MySubscription;
use Filament\Forms\Components\Tabs;
use Filament\Navigation\NavigationItem;
use Filament\Resources\Resource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Subscription\app\Enums\SubscriptionIntervalEnum;
use Modules\Subscription\app\Filament\Resources\PlanResource\pages\CreatePlan;
use Modules\Subscription\app\Filament\Resources\PlanResource\pages\EditPlan;
use Modules\Subscription\app\Filament\Resources\PlanResource\pages\ListPlan;
use Modules\Subscription\app\Filament\Resources\PlanResource\pages\SubscriptionPlans;
use Modules\Subscription\app\models\Plan;
use Modules\Subscription\app\models\Feature;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;

class PlanResource extends Resource
{
    protected static ?string $model = Plan::class;
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make()
                    ->schema([
                        Tabs::make('Translations')
                            ->tabs([
                                Tabs\Tab::make('English')
                                    ->icon('heroicon-o-globe-alt')
                                    ->schema([
                                        TextInput::make('name.en')
                                            ->label(__('Name'))
                                            ->placeholder(__('Enter name in English'))
                                            ->required(),
                                        Forms\Components\Textarea::make('description.en')
                                            ->label(__('Description'))
                                            ->placeholder(__('Enter description in English'))
                                            ->required(),
                                    ]),
                                Tabs\Tab::make('Arabic')
                                    ->icon('heroicon-o-globe-alt')
                                    ->schema([
                                        TextInput::make('name.ar')
                                            ->label(__('Name'))
                                            ->placeholder(__('Enter name in Arabic'))
                                            ->required(),
                                        Forms\Components\Textarea::make('description.ar')
                                            ->label(__('Description'))
                                            ->placeholder(__('Enter description in Arabic'))
                                            ->required(),
                                    ]),
                            ])->columnSpanFull(),

                        Grid::make(2)
                            ->schema([
                                TextInput::make('price')
                                    ->label(__('Price'))
                                    ->placeholder(__('Enter Price'))
                                    ->numeric()
                                    ->required(),

                                TextInput::make('discount_price')
                                    ->label(__('Discount Price'))
                                    ->placeholder(__('Enter discount price'))
                                    ->numeric()
                                    ->nullable(),

                                Select::make('interval')
                                    ->label(__('Interval'))
                                    ->options(SubscriptionIntervalEnum::toArray())
                                    ->enum(SubscriptionIntervalEnum::class)
                                    ->required(),

                                TextInput::make('interval_count')
                                    ->label(__('Interval Count'))
                                    ->placeholder(__('Enter interval count'))
                                    ->numeric()
                                    ->required(),

                                TextInput::make('order')
                                    ->label(__('Order'))
                                    ->placeholder(__('Enter order'))
                                    ->numeric()
                                    ->required(),

                                Toggle::make('is_default')
                                    ->label(__('Is Default')),
                            ]),

                        Repeater::make('planFeatures')
                            ->label(__('Plan Features'))
                            ->relationship()
                            ->schema([
                                Select::make('feature_id')
                                    ->label(__('Feature'))
                                    ->placeholder(__('Select a feature'))
                                    ->options(Feature::query()->pluck('name', 'id'))
                                    ->required(),

                                TextInput::make('value')
                                    ->label(__('Value'))
                                    ->placeholder(__('Enter value'))
                                    ->required(),

//
                            ])
                            ->columnSpanFull()
                            ->itemLabel(__('Feature'))
                            ->addActionLabel(__('Add Feature'))
                            ->reorderableWithButtons()
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('price')
                    ->label(__('Price'))
                    ->money()
                    ->sortable(),

                Tables\Columns\TextColumn::make('discount_price')
                    ->label(__('Discount Price'))
                    ->money()
                    ->sortable(),

                Tables\Columns\TextColumn::make('interval')
                    ->label(__('Interval'))
                    ->sortable(),


                Tables\Columns\TextColumn::make('order')
                    ->label(__('Order'))
                    ->sortable(),


                Tables\Columns\IconColumn::make('is_default')
                    ->label(__('Is Default'))
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('Is Active'))
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label(__('Is Active')),
                Tables\Filters\TernaryFilter::make('is_default')
                    ->label(__('Is Default')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => ListPlan::route('/'),
            'create' => CreatePlan::route('/create'),
            'edit' => EditPlan::route('/{record}/edit'),
        ];
    }
    public static function getNavigationGroup(): string
    {
        return  __('Subscription Management');
    }
    public static function getNavigationLabel(): string
    {
        return __("Plans");
    }
    public static function getBreadcrumb(): string
    {
        return __("Plans");
    }


    public static function getModelLabel(): string
    {
        return __('Plans');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Plans');
    }

}
