<?php

namespace Modules\Subscription\app\Filament\Resources\FeatureResource\pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Subscription\app\Filament\Resources\FeatureResource;

class ListFeatures extends ListRecords
{
    protected static string $resource = FeatureResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
