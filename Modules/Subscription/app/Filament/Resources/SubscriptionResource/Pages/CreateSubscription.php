<?php

namespace Modules\Subscription\app\Filament\Resources\SubscriptionResource\Pages;

use App\Models\User;
use Modules\Subscription\app\Enums\SubscriptionStatusEnum;
use Modules\Subscription\app\Filament\Resources\SubscriptionResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Modules\Subscription\app\models\Plan;

class CreateSubscription extends CreateRecord
{
    protected static string $resource = SubscriptionResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['subscriber_type'] = User::class;
        $data['start_at'] = now();
        $data['expired_at'] = now()->addYear();
        $data['status'] = SubscriptionStatusEnum::ACTIVE->value;
        $data['is_current'] = true;

        // Get plan price
        $plan = \Modules\Subscription\app\Models\Plan::find($data['plan_id']);
        $data['price'] = $plan->price;

        return $data;
    }
}
