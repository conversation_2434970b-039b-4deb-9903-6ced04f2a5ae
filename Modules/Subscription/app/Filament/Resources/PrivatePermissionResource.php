<?php

namespace Modules\Subscription\app\Filament\Resources;
use Filament\Resources\Resource;
use Modules\Subscription\app\Filament\Resources\PrivatePermissionResource\pages\PrivatePermissionRoles;
use Modules\Subscription\app\models\PrivatePermission;
class PrivatePermissionResource extends Resource
{
    protected static ?string $model = PrivatePermission::class;


    public static function getPages(): array
    {
        return [
            'index' => PrivatePermissionRoles::route('/'),
        ];
    }

    public static function getNavigationGroup(): string
    {
        return  __('Subscription Management');
    }

    public static function getNavigationLabel(): string
    {
        return __("Private Permissions");
    }
    public static function getBreadcrumb() : string
    {
        return __('Private Permissions');
    }
    public static function getModelLabel(): string
    {
        return __('Private Permissions');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Private Permissions');
    }
}
