<?php

namespace Modules\Subscription\app\Filament\Resources;

use App\Models\User;
use App\Shared\Components\DateRangeFilter;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Modules\Subscription\app\Enums\SubscriptionStatusEnum;
use Modules\Subscription\app\Filament\Resources\SubscriptionResource\Pages\CreateSubscription;
use Modules\Subscription\app\Filament\Resources\SubscriptionResource\Pages\EditSubscription;
use Modules\Subscription\app\Filament\Resources\SubscriptionResource\Pages\ListSubscriptions;
use Modules\Subscription\app\models\Plan;
use Modules\Subscription\app\models\Subscription;
use Modules\Subscription\app\Filament\Resources\SubscriptionResource\Pages;
use Modules\Subscription\app\Filament\Resources\SubscriptionResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;

class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make()
                    ->schema([
                        Select::make('subscriber_id')
                            ->label('User')
                            ->options(User::query()->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required(),

                        Select::make('plan_id')
                            ->label('Plan')
                            ->relationship('plan', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('subscriber.name')
                    ->label('Subscriber')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('plan.name')
                    ->label(__('Plan'))
                    ->searchable()
                    ->sortable()
                    ->tooltip('Subscription plan type')
                    ->badge(),

                Tables\Columns\TextColumn::make('price')
                    ->label(__('Price'))
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                        );
                    })
                    ->alignment('right'),

                Tables\Columns\TextColumn::make('start_at')
                    ->label(__('Start At'))
                    ->searchable()
                    ->sortable()
                    ->dateTime('M d, Y')
                    ->tooltip(fn ($record) => $record->start_at)
                    ->color('success'),

                Tables\Columns\TextColumn::make('expired_at')
                    ->label(__('Expire At'))
                    ->searchable()
                    ->sortable()
                    ->dateTime('M d, Y')
                    ->tooltip(fn ($record) => $record->expired_at)
                    ->color(fn ($record) =>
                    $record->expired_at?->isPast() ? 'danger' :
                        ($record->expired_at?->diffInDays(now()) <= 7 ? 'warning' : 'success')
                    ),

                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        SubscriptionStatusEnum::ACTIVE->value => 'success',
                        SubscriptionStatusEnum::NEW->value => 'info',
                        SubscriptionStatusEnum::CANCELED->value => 'danger',
                        SubscriptionStatusEnum::FINISHED->value => 'warning',
                        default => 'gray',
                    })
                    ->icons([
                        'heroicon-o-check-circle' => fn ($state) => $state === SubscriptionStatusEnum::ACTIVE->value,
                        'heroicon-o-plus-circle' => fn ($state) => $state === SubscriptionStatusEnum::NEW->value,
                        'heroicon-o-x-circle' => fn ($state) => $state === SubscriptionStatusEnum::CANCELED->value,
                        'heroicon-o-clock' => fn ($state) => $state === SubscriptionStatusEnum::FINISHED->value,
                    ])
                    ->formatStateUsing(fn (string $state): string => SubscriptionStatusEnum::trans($state))
            ])
            ->filters([
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSubscriptions::route('/'),
            'create' =>CreateSubscription::route('/create'),
            'edit' => EditSubscription::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): string
    {
        return  __('Subscription Management');
    }
    public static function getNavigationLabel(): string
    {
        return __("Subscription");
    }
    public static function getBreadcrumb(): string
    {
        return __("Subscription");
    }


    public static function getModelLabel(): string
    {
        return __('Subscription');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Subscription');
    }

}
