<?php

namespace Modules\Subscription\Services;

use Carbon\Carbon;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Subscription\app\Enums\SubscriptionIntervalEnum;
use Modules\Subscription\app\Enums\SubscriptionStatusEnum;
use Modules\Subscription\Repositories\PlanRepository;

use Modules\Subscription\Repositories\SubscriptionRepository;

class SubscriptionService extends ServiceAbstract
{

    public function __construct(SubscriptionRepository $repository, private PlanRepository $planRepository)
    {
        parent::__construct($repository);
    }

    public function create(array $data)
    {

        $account = (array_key_exists('account', $data)) ? $data['account'] : auth()->user();
        $plan = $this->planRepository->findOrFail($data['plan_id']);
        $usage = $this->mapUsage($plan, $account);
        $subscription = parent::create($this->mapperFromPlan($plan, $account));
        $subscription->subscriptionUsages()->createMany($usage);
        return $subscription;
    }


    private function mapperFromPlan($plan, $account): array
    {

        //todo start_at (if have prev subscription ) and parent_subscription (if have prev subscription )
        $subscription = [];
        $currentDat = new \DateTime();

        $subscription['subscriber_type'] = $account::class;
        $subscription['subscriber_id'] = $account->id;
        $subscription['price'] = $plan->discount_price ?? $plan->price;
        $subscription['plan_id'] = $plan->id;
        $subscription['expired_at'] = SubscriptionIntervalEnum::calculatins($plan->interval, $plan->interval_count);
        $subscription['start_at'] = Carbon::today();
        $subscription['status'] = SubscriptionStatusEnum::NEW->value;
        return $subscription;

    }

    private function mapUsage($plan, $account)
    {

        $usage = [];
        foreach ($plan->permissionFeatures as $item) {
            $this->assignPermission($account, $item);

            $usage[] = [
                "feature_id" => $item->id,
                "value" => $item->pivot->value ?? 0
            ];
        }

        return $usage;


    }

    private function assignPermission($account, $feature)
    {
        $account->givePermissionTo($feature->private_permission->permission->name);
    }


}


