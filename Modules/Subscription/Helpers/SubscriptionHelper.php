<?php

namespace Modules\Subscription\Helpers;

use App\Enums\RoleEnum;
use Modules\Subscription\app\models\PrivatePermission;

class SubscriptionHelper
{
    public static function checkPermissionInActivePlan(string $permission_name): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }
        if ($user->hasRole(RoleEnum::ADMIN)) {
            return true; //pass super admin
        }
        $privatePermission = PrivatePermission::whereHas("permission", function ($query) use ($permission_name) {
            $query->where('name', $permission_name);
        })->first();

        if ($privatePermission) {
            $activeSubscription = $user->activeSubscription;

            if (!$activeSubscription) {
                return false;
            }

            // Check if the active plan has the permission
            return  $activeSubscription->plan
                ->features()
                ->where('private_permission_id', $privatePermission->id)
                ->exists();
        } else {
            return true;
        }
    }
}
