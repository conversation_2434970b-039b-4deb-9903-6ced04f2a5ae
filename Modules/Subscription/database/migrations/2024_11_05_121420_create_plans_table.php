<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();

            $table->json('name');
            $table->json('description')->nullable();
            $table->integer('order')->default(1);
            $table->decimal('price',10,2)->unsigned()->default(0);
            $table->decimal('discount_price',10,2)->unsigned()->nullable();
            $table->enum('interval',\Modules\Subscription\app\Enums\SubscriptionIntervalEnum::values());
            $table->integer('interval_count')->default(0);
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('subscription_count_as_user')->nullable();
            $table->integer('subscription_count_as_total')->nullable(); 
            $table->softDeletes();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
