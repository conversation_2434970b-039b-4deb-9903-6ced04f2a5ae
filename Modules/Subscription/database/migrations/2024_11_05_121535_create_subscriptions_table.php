<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();

            $table->morphs('subscriber');
            $table->foreignId('plan_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();

            $table->decimal('price', 10, 2);
            $table->timestamp('start_at')->useCurrent();
            $table->timestamp('expired_at');
            $table->timestamp('canceled_at')->nullable();
            $table->enum('status',\Modules\Subscription\app\Enums\SubscriptionStatusEnum::values());
            $table->boolean('is_current')->default(false);
            $table->foreignId('subscription_id')->nullable()->constrained()->cascadeOnDelete()->cascadeOnUpdate();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
