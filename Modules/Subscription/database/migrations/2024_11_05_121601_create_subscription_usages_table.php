<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_usages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('feature_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('private_permission_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->string('value')->nullable();
            $table->string('used')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_usages');
    }
};
