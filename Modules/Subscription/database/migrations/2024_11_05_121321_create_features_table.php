<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //todo :: ejar integration will be as a seeder and hardcoded in the ejar service
        Schema::create('features', function (Blueprint $table) {
            $table->id();

            $table->json('name');
            $table->json('description')->nullable();

            //based on integration
            $table->foreignId('private_permission_id')->nullable()
                ->constrained('private_permissions')
                ->cascadeOnDelete()
                ->cascadeOnUpdate();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('features');
    }
};
