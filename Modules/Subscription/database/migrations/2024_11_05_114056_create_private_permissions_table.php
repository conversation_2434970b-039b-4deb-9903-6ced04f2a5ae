<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //todo : create this table if the client want spatie permission integration
        //todo : check if for api ,web or all gurads
        Schema::create('private_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('permission_id')->unique()->nullable();
            $table->string('name')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('private_permissions');
    }
};
