<?php
namespace Modules\EjarIntegration\PropertyRetrieving;

use Modules\EjarIntegration\Interfaces\RetrievePropertiesAbstract;

Class RetrieveIndividualEntityDetails extends RetrievePropertiesAbstract
{
    public array $data = [];

    /**
     * @throws \Exception
     */
    public function retrieveEntityDetails(array $data): void
    {
        try {
            $url = $this->base_url . 'PostIndividualEntity';

            $this->post($url, $data);
            $res = json_decode($this->httpResponse['data'], true);

            if ($this->httpResponse['code'] == 200) {
                if ($res['Body']['data']['attributes']['verification_status'] == "verification_succeed") {
                    $this->data = $res['Body']['data'];
                }else {
                    throw new \Exception(__("The entity data is not verified with national ID") . ' ' . $data['id_number']);
                }
            } else {
                if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                    $msg = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                } else {
                    $msg = __("Something went wrong when retrieving entity details of national id") . ' ' . $data['id_number'];
                }
                throw new \Exception($msg);
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
