<?php
namespace Modules\EjarIntegration\LeaseRetrieving;

use Illuminate\Support\Facades\Log;
use Modules\EjarIntegration\Interfaces\RetrieveLeasesAbstract;
use Modules\Lease\app\Jobs\ProcessRetrievalLeases;

Class RetrieveLeases extends RetrieveLeasesAbstract
{
    public array $leases = [];


    public function getLeasesCount()
    {
        try {
            $url = $this->base_url . 'GetContracts';
            $result = $this->get($url, null);
            $size = json_decode($result['data'])->Body->meta->count;
            return $size;

        } catch (\Exception $e) {

            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public function retrieveLeases($company , $user , $page , $perPage)
    {
        try {
                $url = $this->base_url . 'GetContracts';
    
                $params = ['page[number]' => $page, 'page[size]' => $perPage];
                
                $this->get($url, $params);
                $res = json_decode($this->httpResponse['data'], true);

                if ($this->httpResponse['code'] == 200) {
                    $leases = $res['Body']['data'] ?? [];
                    $leasesDetails = [];
                    foreach ($leases as $lease) {
                        $leaseId = $lease['id'] ?? null;
                        if ($leaseId) {
                            $details = $this->getLeaseDetails($leaseId);
                            // if ($details) {
                                $leasesDetails[] = $details;
                            // }
                        }
                    }
                    return $leasesDetails;
                } else {
                    if (isset($res['Body']['errors'][0]['detail'][app()->getLocale()])) {
                        $msg = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                    } else {
                        $msg = __("Something went wrong on page $page.");
                    }
                    throw new \Exception($msg);
                }
        } catch (\Exception $e) {
 
            throw new \Exception($e->getMessage());
        }
    }

    public function retrieveLeaseByNumber($lease_number , $page , $perPage)
    {
        try {
                $url = $this->base_url . 'GetContracts';
    
                $params = ['page[number]' => $page, 'page[size]' => $perPage];
                
                $this->get($url, $params);
                $res = json_decode($this->httpResponse['data'], true);

                if ($this->httpResponse['code'] == 200) {
                    $leases = $res['Body']['data'] ?? [];
                    foreach ($leases as $lease) {
                        
                        $leaseId = $lease['id'] ?? null;
                        $leaseNumber = $lease['attributes']['contract_number'] ?? null;

                        if ($leaseId && ($leaseNumber == $lease_number)) {
                            $details = $this->getLeaseDetails($leaseId);
                            return $details;
                        }
                    }
                } else {
                    if (isset($res['Body']['errors'][0]['detail'][app()->getLocale()])) {
                        $msg = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                    } else {
                        $msg = __("Something went wrong on page $page.");
                    }
                    throw new \Exception($msg);
                }
        } catch (\Exception $e) {
 
            throw new \Exception($e->getMessage());
        }
    }

    public function getLeaseInvoices(string $contractId)
    {
        try {
            $url = $this->base_url . 'GetContractInvoices';
            $params = ['ContractId' => $contractId];
            $result = json_decode($this->get($url, $params)['data']);

            return $result;

        } catch (\Exception $e) {
            Log::error("Exception fetching lease details: " . $e->getMessage());
            return null;
        }
    }
    

    protected function getLeaseDetails(string $contractId)
    {
        try {
            $url = $this->base_url . 'GetContractDetails';
            $params = ['ContractId' => $contractId];
            $result = json_decode($this->get($url, $params)['data']);

            return $result;

        } catch (\Exception $e) {
            Log::error("Exception fetching lease details: " . $e->getMessage());
            return null;
        }
    }
}
