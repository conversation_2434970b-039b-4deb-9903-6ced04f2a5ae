<?php
namespace Modules\EjarIntegration\PropertyRetrieving;

use Modules\EjarIntegration\Interfaces\RetrievePropertiesAbstract;

class RetrieveOwnershipDocumentOwners extends RetrievePropertiesAbstract
{
    public array $owners = [];
    public array $included = [];
    public array $meta = [];

    /**
     * @throws \Exception
     */
    public function retrieveDocumentOwners(int $documentId): void
    {
        try {
            $url = $this->base_url . 'GetOwnershipDocumentOwners';

            $this->get($url, ["DocumentId" => $documentId]);
            $res = json_decode($this->httpResponse['data'], true);

            if ($this->httpResponse['code'] == 200) {
                $this->owners = $res['Body']['data'];
                $this->included = $res['Body']['included'];
                $this->meta = $res['Body']['meta'];
            } else {
                if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                    $msg = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                } else {
                    $msg = __("Something went wrong when retrieving document owners.");
                }
                throw new \Exception($msg);
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
