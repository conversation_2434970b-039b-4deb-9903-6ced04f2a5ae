<?php
namespace Modules\EjarIntegration\PropertySynchronizationSteps;

use Modules\EjarIntegration\Interfaces\ISyncInterface;
use Modules\EjarIntegration\Interfaces\SyncStepAbstract;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\EjarSyncStatus;

Class SyncPropertyUnits extends SyncStepAbstract implements ISyncInterface
{
    public function sync(): array
    {
        try {
            if (is_null($this->property->ejar_uuid)) {
                $this->response['status'] = false;
                $this->response['message'] = __("You should sync the property before trying to synchronize units");
                return $this->response;
            }
            $url = $this->base_url . 'PostUnit?PropertyId=' . $this->property->ejar_uuid;
            $units = $this->property->units;
            $collectRes = [];
            foreach ($units as $unit) {
                $requestData = $this->prepareUnitData($unit);
                $this->post($url, $requestData);
                $res = json_decode($this->httpResponse['data'], true);
                if ($this->httpResponse['code'] == 201 || $this->httpResponse['code'] == 200) {
                    //syncing data
                    $unit->ejar_uuid = $res['Body']['data']['id'];
                    $unit->ejar_sync_status = EjarSyncStatus::SYNCED;
                    $unit->last_synced_at = now();
                    $unit->save();
                    $collectRes[] = $res;
                } elseif (isset($res['Body']['errors']) && $res['Body']['errors'][0]['code'] == "E1279") { //"E1279" refers to that unit already exists
                    $this->response['message'] = __("This unit already added");
                } else {
                    $unit->ejar_sync_status = EjarSyncStatus::FAILED;
                    $unit->last_synced_at = now();
                    $unit->save();
                    $this->response['status'] = false;
                    $this->response['data'] = $res;
                    if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                        $this->response['message'] = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                    } else {
                        $this->response['message'] = __("Something went wrong, please try again later.");
                    }
                    return $this->response;
                }
            }
            $this->response['message'] = __("Units linked with this property at ejar successfully");
            $this->response['data'] = $collectRes;
        } catch (\Exception $e) {
            $this->response['status'] = false;
            $this->response['message'] =$e->getMessage();
        }

        return $this->response;
    }

    public function prepareUnitData(Property $unit): array
    {
        $unitAttributes = collect($unit->propertyAttributes);
        return [
            "data" => [
                "attributes" => [
                    "unit_number" => $unit->number,
                    "unit_type" => $unit->property_type?->key ?? "apartment",
                    "unit_usage" => $unit->usability?->key ?? "residential_families",
                    "amenities" => [], //todo check
                    "utilities" => [], //todo adapt utilities
                    "unit_dimensions" => [], //todo handle dimensions
                    "floor_number" => $unitAttributes->firstWhere("key", "floor_number")?->pivot->value,
                    "units_per_floor" => $unitAttributes->firstWhere("key", "units_per_floor")?->pivot->value,
                    "area" => $unitAttributes->firstWhere("key", "area")?->pivot->value,
                    "rooms" => $unitAttributes->firstWhere("key", "rooms")?->pivot->value,
                    //'furnish_type' => $unitAttributes->firstWhere("key", "furnish_type")?->pivot->value, //todo select furnish type option
                ]
            ]
        ];
    }
}
