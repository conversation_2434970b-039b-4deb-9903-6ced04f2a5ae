<?php
namespace Modules\EjarIntegration\PropertySynchronizationSteps;

use App\Enums\DocumentTypeEnum;
use <PERSON>iusTS\HijriDate\Hijri;
use Modules\EjarIntegration\Interfaces\ISyncInterface;
use Modules\EjarIntegration\Interfaces\SyncStepAbstract;
use Modules\Property\Enums\PropertySyncStatus;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;

Class SyncOwnershipDocs extends SyncStepAbstract implements ISyncInterface
{
    public function sync(): array
    {
        try {
            $media = $this->property->documentOwnership?->getMedia('ownership_document');
            if (empty($media)) {
                throw new \Exception(__("No ownership document found"));
            }
            $documentFilesFields = $this->prepareDocumentFiles($media);
            //todo add issued_by & issued_place
            $multipartData = [
                [
                    'name' => 'data[type]',
                    'contents' => DocumentTypeEnum::OWNERSHIP_DOCUMENT->value,
                ],
                [
                    'name' => 'data[attributes][ownership_document_type]',
                    'contents' => $this->property->documentOwnership->metadata['document_type'] ?? null,
                ],
                [
                    'name' => 'data[attributes][document_number]',
                    'contents' => $this->property->documentOwnership->metadata['ownership_reference_no'] ?? null,
                ],
                [
                    'name' => 'data[attributes][legal_document_type_name]',
                    'contents' => $this->property->documentOwnership->metadata['legal_document_type_name'] ?? null,
                ],
                [
                    'name' => 'data[attributes][issued_date]',
                    'contents' => $this->getHejriIssueDate(),
                ],
                [
                    'name' => 'data[attributes][region_code]',
                    'contents' => $this->property->region_id, //1
                ],
                [
                    'name' => 'data[attributes][city_code]',
                    'contents' => $this->property->city_id, //"10105"
                ],
                [
                    'name' => 'data[attributes][district_code]',
                    'contents' => $this->property->district_id, //"2143"
                ]
            ];
            foreach ($documentFilesFields as $fileField) {
                $multipartData[] = $fileField;
            }
            $this->appendToHeaders(['Accept' => '*/*']);
            if ($this->step->status == PropertySyncStatus::COMPLETED || $this->step->status == PropertySyncStatus::NEED_SYNC) {
                $url = $this->base_url . 'PatchOwnershipDocument?DocumentId=' . $this->property->documentOwnership->ejar_id;
                $this->patchMultipartForm($url, $multipartData);
            } else {
                $multipartData[] = [
                    'name' => 'data[attributes][property_count]',
                    'contents' => "10", //todo get property_count dynamically
                ];
                $url = $this->base_url . 'PostOwnershipDocument';
                $this->postMultipartForm($url, $multipartData);
            }
            //dd(['url'=> $url, 'data' => $multipartData, 'res' => $this->httpResponse]);
            $res = json_decode($this->httpResponse['data'], true);
            if ($this->httpResponse['code'] == 200) {
                //sync data
                $this->property->documentOwnership->update(['ejar_id' => $res['Body']['data']['id']]);
                $this->response['message'] = __("Ownership document has been synchronized");
            } else {
                $this->response['status'] = false;
                if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                    $this->response['message'] = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                } else {
                    $this->response['message'] = __("Something went wrong, please try again later.");
                }
            }
            $this->response['data'] = $res;
        } catch (\Exception $e) {
            $this->response['status'] = false;
            $this->response['message'] =$e->getMessage();
        }

        return $this->response;
    }


    protected function prepareDocumentFiles(MediaCollection $files): array
    {
        $append_media= [];
        foreach ($files as $file) {
            $filePath = $file->getPath();
            if (!file_exists($filePath)) {
                throw new \Exception(__("Document file does not exists!."));
            }

            $append_media[] = [
                'name' => 'data[attributes][scanned_documents][]',
                'contents' => fopen($filePath, 'r'),
                'filename' => $file->file_name
            ];
        }

        return $append_media;
    }

    protected function getHejriIssueDate(): string
    {
        $date = date('Y-m-d', strtotime($this->property->documentOwnership->metadata['issue_date'])) ?? null;
        try {
            return Hijri::convertToHijri($date)->format('Y-m-d');
        } catch (\Exception $e) {
            return $date;
        }
    }
}
