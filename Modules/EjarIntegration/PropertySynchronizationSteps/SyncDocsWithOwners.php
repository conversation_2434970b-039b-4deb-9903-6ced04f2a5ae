<?php
namespace Modules\EjarIntegration\PropertySynchronizationSteps;

use Modules\EjarIntegration\Enums\EjarDocumentTypes;
use Modules\EjarIntegration\Enums\EjarEntityTypes;
use Modules\EjarIntegration\Enums\EjarPropertyOwners;
use Modules\EjarIntegration\Interfaces\ISyncInterface;
use Modules\EjarIntegration\Interfaces\SyncStepAbstract;
use Modules\Organization\app\Models\Organization;
use Modules\Property\app\Models\PropertyOwners;

Class SyncDocsWithOwners extends SyncStepAbstract implements ISyncInterface
{
    protected string $url;
    public function sync(): array
    {
        try {
            $DocumentId = $this->property->documentOwnership->ejar_id;
            if (is_null($DocumentId)) {
                $this->response['status'] = false;
                $this->response['message'] = __("You should sync the document before trying to link it with owner");
                return $this->response;
            }
            $url = $this->base_url . 'PostOwnershipDocumentOwner?DocumentId=' . $DocumentId;
            $this->url = $url;
            $owners = $this->property->owners;
            if ($owners->isEmpty()) {
                $this->response['status'] = false;
                $this->response['message'] = __("There is no owner associated with this property");

                return $this->response;
            }
            $collectRes = [];

            foreach ($owners as $owner) {

                $requestData = $this->getPropertyOwnersRequestData($owner);
                $this->post($url, $requestData);
                $res = json_decode($this->httpResponse['data'], true);
                if ($this->httpResponse['code'] == 201 || $this->httpResponse['code'] == 200) {
                    //syncing data
                    $owner->ejar_uuid = $res['Body']['data']['id'];
                    $owner->save();
                    $collectRes[] = $res;
                } elseif (isset($res['Body']['errors']) && $res['Body']['errors'][0]['code'] == "E2572") { //"E2572" refers to that entity already exists
                    $this->response['message'] = __("This owner already added");
                } elseif (isset($res['Body']['errors']) && $res['Body']['errors'][0]['code'] == "E2570") { //"E2570" refers to that representative already assigned for this document
                    $this->response['message'] = __("This owner already added");
                } else {
                    $this->response['status'] = false;
                    $this->response['data'] = $res;
                    if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                        $this->response['message'] = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                    } else {
                        $this->response['message'] = __("Something went wrong, please try again later.");
                    }
                    return $this->response;
                }
            }
            $this->response['message'] = __("Owners linked with this document at ejar successfully");
            $this->response['data'] = $collectRes;
        } catch (\Exception $e) {
            $this->response['status'] = false;
            $this->response['message'] =$e->getMessage();
        }

        return $this->response;
    }

    public function getOwnerDocsFormData(string $doc_type, string $role, string $entity_uuid, string $entity_type, bool $is_representer = false): array
    {
        return [
            'data' => [
                "type" => $doc_type,
                "attributes" => [
                    "role" => $role,
                    "is_representative" => $is_representer,
                ],
                "relationships" => [
                    "entity" => [
                        "data" => [
                            "id" => $entity_uuid,
                            "type" => $entity_type,
                        ]
                    ]
                ]
            ]
        ];
    }

    public function getPropertyOwnersRequestData(PropertyOwners $owner): array
    {
        $is_representer = $owner->is_representer == 1;
        $role = EjarPropertyOwners::OWNER->value;
        if ($owner->percentage == 0 && $is_representer) { //if zero percentage then this is a representer
            $role = EjarPropertyOwners::REPRESENTATIVE->value;
        }
        if ($owner->ownerable_type == Organization::class) {
            $data = $this->getOwnerDocsFormData(
                EjarDocumentTypes::OWNERSHIP_DOC->value, $role,
                $owner->ownerable->ejar_uuid ?? null, EjarEntityTypes::ORGANIZATION_ENTITIES->value,
                $is_representer
            );
        } else {
            $data = $this->getOwnerDocsFormData(
                EjarDocumentTypes::OWNERSHIP_DOC->value, $role,
                $owner->ownerable->ejar_uuid ?? null, EjarEntityTypes::INDIVIDUAL_ENTITIES->value,
                $is_representer
            );
        }
        return $data;
    }

}
