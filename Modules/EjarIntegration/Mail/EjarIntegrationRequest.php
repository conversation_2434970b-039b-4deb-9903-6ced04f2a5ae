<?php

namespace Modules\EjarIntegration\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Modules\Company\app\Models\Company;

class EjarIntegrationRequest extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        protected Company $company
    ) {}

    public function build()
    {
        return $this->from(env('MAIL_FROM_ADDRESS'), $this->company->name)  // Use authorized sender email
            ->cc($this->company->email)
            ->to(env('EJAR_EMAIL_ADDRESS', env('MAIL_FROM_ADDRESS')))
            ->subject(__('Ejar Integration Request company') . ' ' . $this->company->name)
            ->view('ejarintegration::emails.ejar-integration-request')
            ->with([
                'company' => $this->company,
            ]);
    }
}
