<?php

namespace Modules\EjarIntegration\app\Helpers;

use Modules\EjarIntegration\app\Models\RetrievalLog as RetrievalLogModel;
use Modules\EjarIntegration\Enums\RetrievalStatus;

class RetrievalLogger {

    public static function success(int $companyId, string $property, string $msg, array $details = null): void
    {
        RetrievalLogModel::create([
            'company_id' => $companyId,
            'property_name' => $property,
            'status' => RetrievalStatus::COMPLETED,
            'message' => $msg,
            'details' => $details ? json_encode($details) : null,
        ]);
    }

    public static function failed(int $companyId, string $property, string $msg, array $details = null): void
    {
        RetrievalLogModel::create([
            'company_id' => $companyId,
            'property_name' => $property,
            'status' => RetrievalStatus::FAILED,
            'message' => $msg,
            'details' => json_encode($details)
        ]);
    }
    public static function clearPreviousLogs(int $companyId): void
    {
        RetrievalLogModel::where(['company_id' => $companyId])->delete();
    }
}
