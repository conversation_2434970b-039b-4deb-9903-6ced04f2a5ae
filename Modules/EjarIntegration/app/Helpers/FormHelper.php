<?php

namespace Modules\EjarIntegration\app\Helpers;

use App\Enums\RoleEnum;
use Filament\Forms\Components\Select;

class FormHelper
{
    public static function CompanyField()
    {
        $user = auth()->user();

        if ($user->hasRole(RoleEnum::ADMIN)){
            return Select::make('company_id')
                ->label(__('Company'))
                ->required()
                ->preload()
                ->searchable()
                ->relationship('company', 'name')
                ->unique(ignorable: fn ($record) => $record)
                ->placeholder(__('Select a Company'))
                ->validationMessages([
                    'required' => __('Company is required'),
                    'unique' => __('Request is already sent for This company'),
                ])
                ->reactive();
        } else{
            $company_id = $user->company_id ?? $user->company?->id;

            return Select::make('company_id')
                ->label(__('Company'))
                ->default($company_id)
                ->disabled()
                ->searchable()
                ->unique(ignorable: fn ($record) => $record)
                ->validationMessages([
                    'required' => __('Company is required'),
                    'unique' => __('Request is already sent for This company'),
                ])
                ->relationship('company', 'name');
        }
    }
}
