<?php
namespace Modules\EjarIntegration\app\Helpers;

use DOMDocument;

trait EjarHttpHelper
{
    private $timeout = 100;
    private array $headers = [];
    private array $extra = [];

    public $httpResponse = ['status' => true, 'message' => '', 'data' => []];

    public function post($uri, $data)
    {
        $httpClient = new \GuzzleHttp\Client(['http_errors' => false]);

        try {
            $arr = array_merge([
                'body' => json_encode($data),
            ], $this->getBodyArray());
            $httpResponse = $httpClient->request('POST', $uri, $arr);
            $this->httpResponse['code'] = $httpResponse->getStatusCode();
            $this->httpResponse['data'] = $httpResponse->getBody()->getContents();
            $this->httpResponse = $this->checkIfResponseContainsXML($this->httpResponse);

            return $this->httpResponse;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function patch($uri, $data)
    {
        $httpClient = new \GuzzleHttp\Client(['http_errors' => false]);

        try {
            $arr = array_merge([
                'body' => json_encode($data),
            ], $this->getBodyArray());
            $httpResponse = $httpClient->request('PATCH', $uri, $arr);
            $this->httpResponse['code'] = $httpResponse->getStatusCode();
            $this->httpResponse['data'] = $httpResponse->getBody()->getContents();
            $this->httpResponse = $this->checkIfResponseContainsXML($this->httpResponse);

            return $this->httpResponse;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function postMultipartForm($uri, $data)
    {
        $httpClient = new \GuzzleHttp\Client(['http_errors' => false]);
        // Prepare multipart data
        $merged = array_merge([
            'multipart' => $data,
        ], $this->getBodyArray());
        try {
            $httpResponse = $httpClient->request('POST', $uri, $merged);
            $this->httpResponse['code'] = $httpResponse->getStatusCode();
            $this->httpResponse['data'] = $httpResponse->getBody()->getContents();
            $this->httpResponse = $this->checkIfResponseContainsXML($this->httpResponse);

            return $this->httpResponse;
        }
        catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function patchMultipartForm($uri, $data)
    {
        $httpClient = new \GuzzleHttp\Client(['http_errors' => false]);
        // Prepare multipart data
        $merged = array_merge([
            'multipart' => $data,
        ], $this->getBodyArray());
        try {
            $httpResponse = $httpClient->request('PATCH', $uri, $merged);
            $this->httpResponse['code'] = $httpResponse->getStatusCode();
            $this->httpResponse['data'] = $httpResponse->getBody()->getContents();
            $this->httpResponse = $this->checkIfResponseContainsXML($this->httpResponse);

            return $this->httpResponse;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function postJson($uri, $data)
    {
        $httpClient = new \GuzzleHttp\Client(['http_errors' => false]);

        try {
            $httpResponse = $httpClient->request('POST', $uri, [
                'headers' => [
                    "X-IBM-Client-Id" => $this->clientId,
                    "X-IBM-Client-Secret" => $this->clientSecret,
                    "RefId" => "1",
                    "Accept" => "application/json",
                    "Content-Type" => "application/json",
                    "Cookie" => "f5avraaaaaaaaaaaaaaaa_session_=FDAFKBEJPJNKNBGDBEDMLCJJPKHMLAHLNGFHMLJCHGKKFCLJJFILJDEFOKPHHDAOEKGDJAGIGAHBIKMFKABABBPBPKCBBPOHOPJLKCNBJHLOJGENKKMEOBJNLJECMFIF; TS41d09454027=088dd7b3b4ab2000e7a9177f705991d2275a98e8339a8a4b485707d86a176352e1a41dbe12e2327c0829dc8ff8113000ee874d2258d3553c3f5934a170b71b29439fd8f4a0775e8207be43259faea2be7314ab399a7be883316c7515b1225274; cooki_N=!vuIeu00BxrLFGlNuJxCYoh0dwC1FwFYaybk5BGqAF+h//eWwltgfLnF/Iht3DgeK9mNd5sRaVWC3"
                ],
                'json' => $data // Correct way to send JSON payload
            ]);

            $this->httpResponse['code'] = $httpResponse->getStatusCode();
            $this->httpResponse['data'] = json_decode($httpResponse->getBody()->getContents(), true);

            return $this->httpResponse;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function get($uri, $data)
    {
        $httpClient = new \GuzzleHttp\Client();
        try {
            $url = $uri;
            $count = 0;
            if (!empty($data)) {
                foreach ($data as $key => $value) {
                    if ($count == 0) {
                        $url .= '?' . $key . '=' . $value;
                        $count++;
                    } else {
                        $url .= '&' . $key . '=' . $value;
                    }
                }
            }
            $httpResponse = $httpClient->request('GET', $url, $this->getBodyArray());

            $this->httpResponse['code'] = $httpResponse->getStatusCode();
            $this->httpResponse['data'] = $httpResponse->getBody()->getContents();
            $this->httpResponse = $this->checkIfResponseContainsXML($this->httpResponse);

            return $this->httpResponse;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function delete($uri, array $data)
    {
        $httpClient = new \GuzzleHttp\Client();
        try {
            $httpResponse = $httpClient->request('DELETE', $uri, array_merge([
                'body' => json_encode($data),
            ], $this->getBodyArray()));
            $this->httpResponse['data'] = $httpResponse->getBody()->getContents();
            $this->httpResponse = $this->checkIfResponseContainsXML($this->httpResponse);

            return $this->httpResponse;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function setHttpHeaders(array $headers)
    {
        $this->headers = $headers;
    }

    public function appendToHeaders(array $options)
    {
        $this->headers = array_merge($this->headers, $options);
    }

    public function setHttpExtra(array $extra)
    {
        $this->extra = $extra;
    }

    private function getBodyArray(): array
    {
        return array_merge([
            'headers' => $this->headers,
            "connect_timeout" => $this->timeout,
            'timeout' => $this->timeout,
        ], $this->extra);
    }

    public function setTimeout($timeout)
    {
        $this->timeout = $timeout;
    }

    /**
     * @throws \Exception
     */
    public function checkIfResponseContainsXML(array $response): array
    {
        // Check if the response contains XML
        if (isset($response['data']))
        {
            // Check if it's XML (or HTML pretending to be XML)
            libxml_use_internal_errors(true); // suppress XML errors

            $xml = simplexml_load_string($response['data']);

            if ($xml !== false) {
                // It's XML or HTML that can be parsed
                $dom = new DOMDocument();
                $dom->loadHTML($response['data']);

                $title = $dom->getElementsByTagName('title')->item(0)?->nodeValue;
                $p = $dom->getElementsByTagName('p')->item(0)?->nodeValue;

                $msg = trim($title) . ' - ' . trim($p);

                if (!in_array($response['code'], [200, 201])) {
                    throw new \Exception($msg, $response['code']);
                } else {
                    $response['message'] = $msg;
                }
            }
        }

        return $response;
    }
}
