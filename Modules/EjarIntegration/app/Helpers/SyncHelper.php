<?php

namespace Modules\EjarIntegration\app\Helpers;

use Modules\Property\app\Models\SyncPropertyStep;
use Modules\Property\Enums\PropertySyncStatus;
use Modules\Property\Enums\PropertySyncStep;
use Modules\Subscription\Helpers\SubscriptionHelper;

class SyncHelper {
    public static function syncAvailableInActivePlan(): bool
    {
        // just check that permission in active plan
        return SubscriptionHelper::checkPermissionInActivePlan('sync_property');
    }

    public static function syncAvailableLeaseInActivePlan(): bool
    {
        // just check that permission in active plan
        return SubscriptionHelper::checkPermissionInActivePlan('sync_lease');
    }

    public static function markPropertySyncStepsAsCompleted(int $property_id): void
    {
        $steps = SyncPropertyStep::where(['property_id' => $property_id]);
        if ($steps->get()->isEmpty()) {
            $insertable = [];
            foreach (PropertySyncStep::cases() as $step) {
                $insertable[] = [
                    'property_id' => $property_id,
                    'step' => $step->value,
                    'status' => PropertySyncStatus::COMPLETED->value,
                    'started_at' => now(),
                    'completed_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            SyncPropertyStep::insert($insertable);
        }else {
            $steps->update([
                'status' => PropertySyncStatus::COMPLETED->value,
                'error_message' => null,
                'response_data' => null,
                'started_at' => now(),
                'completed_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
