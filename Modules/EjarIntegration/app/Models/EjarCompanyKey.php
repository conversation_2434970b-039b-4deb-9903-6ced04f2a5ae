<?php

namespace Modules\EjarIntegration\app\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Company\app\Models\Company;
use Modules\Tenancy\Traits\BelongsToTenancy;

class EjarCompanyKey extends Model
{
    use HasFactory;
    use BelongsToTenancy; //to filter companies based on tenancy

    /**
     * The attributes that are mass assignable.
     */
    protected $guarded = ['id'];

    protected $casts = [
        'response_received_at' => 'datetime',
    ];
    public function requestSender(){
        return $this->belongsTo(User::class, 'request_sent_by');
    }

    public function company(){
        return $this->belongsTo(Company::class);
    }
}
