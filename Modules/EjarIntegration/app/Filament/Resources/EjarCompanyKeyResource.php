<?php

namespace Modules\EjarIntegration\app\Filament\Resources;

use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Modules\EjarIntegration\app\Filament\Actions\ResendIntegrationRequestMailAction;
use Modules\EjarIntegration\app\Filament\Resources\EjarCompanyKeyResource\Pages;
use Modules\EjarIntegration\app\Helpers\FormHelper;
use Modules\EjarIntegration\app\Models\EjarCompanyKey;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class EjarCompanyKeyResource extends Resource
{
    protected static ?string $model = EjarCompanyKey::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Hidden::make('request_sent_by')->default(\auth()->id()),

                FormHelper::CompanyField(),

                TextInput::make('client_id')
                    ->label(__('Client ID'))
                    ->required()
                    ->unique(ignorable: fn ($record) => $record)
                    ->placeholder(__('Enter Client ID'))
                    ->validationMessages([
                        'required' => __('Client ID is required'),
                        'unique' => __('This Client ID is already exists'),
                    ])
                    ->visible(fn (Get $get, $operation) => $operation != 'create')
                    ->reactive(),

                TextInput::make('client_secret')
                    ->label(__('Client Secret'))
                    ->required()
                    ->unique(ignorable: fn ($record) => $record)
                    ->placeholder(__('Enter Client Secret'))
                    ->validationMessages([
                        'required' => __('Client Secret is required'),
                        'unique' => __('This Client Secret is already exists'),
                    ])
                    ->visible(fn (Get $get, $operation) => $operation != 'create')
                    ->reactive(),


                DateTimePicker::make('response_received_at')
                    ->label(__('Response Received At'))
                    ->native(false)
                    ->formatStateUsing(fn ($record) => $record?->response_received_at ?? now())
                    ->default(fn ($record) => $record?->response_received_at ?? now())
                    ->seconds(true)
                    ->required()
                    ->displayFormat('Y-m-d H:i:s')
                    ->timezone('UTC')
                    ->validationMessages([
                        'required' => __('Please Select received date'),
                    ])
                    ->visible(fn (Get $get, $operation) => $operation != 'create')
                    ->placeholder(__('Select Date and Time'))
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table

            ->columns([
                Tables\Columns\TextColumn::make('company.name')
                    ->label(__('Office Name')),

                Tables\Columns\TextColumn::make('requestSender.name')
                    ->label(__('Request Sent By')),

                Tables\Columns\BooleanColumn::make('request_sent')
                    ->label(__('Request Sent'))
                    ->default(true),

                Tables\Columns\BooleanColumn::make('response_received')
                    ->state(function ($record): bool {
                        return !is_null($record->response_received_at);
                    })
                    ->label(__('Response Received')),

                Tables\Columns\TextColumn::make('client_id')
                    ->label(__('Client ID'))
                    ->badge(),

                Tables\Columns\TextColumn::make('client_secret')
                    ->label(__('Client Secret'))
                    ->badge(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Request Sent at'))
                    ->dateTime()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('response_received_at')
                    ->label(__('Response Received at'))
                    ->dateTime()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                ResendIntegrationRequestMailAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEjarCompanyKeys::route('/'),
            'create' => Pages\CreateEjarCompanyKey::route('/create'),
            'edit' => Pages\EditEjarCompanyKey::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Ejar Integration');
    }

    public static function getBreadcrumb() : string
    {
        return __('Ejar Integration Requests');
    }

    public static function getNavigationLabel(): string
    {
        return __('Ejar Company Keys');
    }
}
