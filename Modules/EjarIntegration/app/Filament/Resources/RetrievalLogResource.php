<?php

namespace Modules\EjarIntegration\app\Filament\Resources;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Modules\EjarIntegration\app\Filament\Resources\RetrievalLogResource\Pages;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\EjarIntegration\app\Models\RetrievalLog;
use Modules\EjarIntegration\Enums\RetrievalStatus;

class RetrievalLogResource extends Resource
{
    protected static ?string $model = RetrievalLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(2)
                ->schema([
                    Select::make('company_id')
                        ->label(__('Office Name'))
                        ->relationship('company', 'name')
                        ->disabled(),

                    TextInput::make('property_name')
                        ->label(__('Property'))
                        ->disabled(),
                ]),
            Grid::make(2)
                ->schema([
                    TextInput::make('status')
                        ->label(__('Status'))
                        ->disabled()
                        ->formatStateUsing(fn ($state): string => RetrievalStatus::tryFrom($state)->getLabel() ?? $state),

                    DatePicker::make('created_at')
                        ->label(__('Started At'))
                        ->disabled()
                        ->native(false),
                ]),
            Grid::make(3)
                ->schema([
                    Textarea::make('message')
                        ->label(__('Message'))
                        ->disabled()
                        ->columnSpanFull(),
                ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table

            ->columns([
                Tables\Columns\TextColumn::make('company.name')
                    ->label(__('Office Name')),

                Tables\Columns\TextColumn::make('property_name')
                    ->label(__('Property Name / Lease #')),

                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    //todo handle color
                    ->badge(),

                Tables\Columns\TextColumn::make('message')
                    ->label(__('Message'))
                    ->badge(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Started at'))
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRetrievalLogs::route('/'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Ejar Integration');
    }

    public static function getBreadcrumb() : string
    {
        return __('Retrieval Logs');
    }

    public static function getNavigationLabel(): string
    {
        return __('Retrieval Logs');
    }
}
