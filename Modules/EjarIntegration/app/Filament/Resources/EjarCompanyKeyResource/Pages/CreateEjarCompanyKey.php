<?php

namespace Modules\EjarIntegration\app\Filament\Resources\EjarCompanyKeyResource\Pages;

use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Mail;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\app\Filament\Resources\EjarCompanyKeyResource;
use Filament\Resources\Pages\CreateRecord;
use Modules\EjarIntegration\Mail\EjarIntegrationRequest;
use Modules\EjarIntegration\Services\EjarIntegrationService;

class CreateEjarCompanyKey extends CreateRecord
{
    protected static string $resource = EjarCompanyKeyResource::class;

    protected EjarIntegrationService $ejarService;
    public function __construct()
    {
        $this->ejarService = new EjarIntegrationService();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterCreate(): void
    {
        try {
            //send integration request mail to ejar
            $company = $this->record->company;
            Mail::send(new EjarIntegrationRequest($company));
            //notify
            Notification::make()
                ->success()
                ->title(__("Mail of Ejar integration request sent successfully to " . $company->email))
                ->icon('heroicon-o-check-circle')
                ->iconColor('success')
                ->duration(5000)
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title(__('Something went wrong while sending integration request mail to ejar.'))
                ->body(__('Please try resend integration request mail to ejar later.'))
                ->danger()
                ->persistent()
                ->send();
        }
    }

    protected function beforeCreate(): void
    {
        // Check company data completeness before request
        $data = $this->form->getState();
        try {
            $company = Company::find($data['company_id']);
            if (empty($company)) {
                Notification::make()
                    ->title(__('Undefined Company'))
                    ->body(__('Please verify that company is exists and ejar integration data such as company name, CR number and company unified number is valid.'))
                    ->danger()
                    ->persistent()
                    ->send();
                $this->halt(); // This stops the creation process
            }
            $this->ejarService->validateCompanyData($company);
        } catch (\Exception $e) {
            Notification::make()
                ->title(__('Missing required company data for Request Ejar integration'))
                ->body($e->getMessage())
                ->danger()
                ->persistent()
                ->send();
            $this->halt(); // This stops the creation process

        }
    }
}
