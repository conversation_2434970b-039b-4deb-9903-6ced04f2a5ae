<?php

namespace Modules\EjarIntegration\app\Filament\Resources\EjarCompanyKeyResource\Pages;

use Modules\EjarIntegration\app\Filament\Resources\EjarCompanyKeyResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditEjarCompanyKey extends EditRecord
{
    protected static string $resource = EjarCompanyKeyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
