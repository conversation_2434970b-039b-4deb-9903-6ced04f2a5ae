<?php

namespace Modules\EjarIntegration\app\Filament\Resources\EjarCompanyKeyResource\Pages;

use Modules\EjarIntegration\app\Filament\Resources\EjarCompanyKeyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEjarCompanyKeys extends ListRecords
{
    protected static string $resource = EjarCompanyKeyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('request ejar integration')),
        ];
    }
}
