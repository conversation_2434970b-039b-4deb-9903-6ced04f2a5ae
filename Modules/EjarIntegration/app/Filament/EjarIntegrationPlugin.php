<?php

namespace Modules\EjarIntegration\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\EjarIntegration\app\Filament\Resources\EjarCompanyKeyResource;
use Modules\EjarIntegration\app\Filament\Resources\RetrievalLogResource;

class EjarIntegrationPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'EjarIntegration';
    }

    public function getId(): string
    {
        return 'ejarintegration';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }
    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                EjarCompanyKeyResource::class,
                RetrievalLogResource::class,
            ]);
    }
}
