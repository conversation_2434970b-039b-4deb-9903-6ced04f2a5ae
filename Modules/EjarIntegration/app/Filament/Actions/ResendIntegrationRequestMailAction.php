<?php
namespace Modules\EjarIntegration\app\Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Model;
use Modules\EjarIntegration\Services\EjarIntegrationService;
class ResendIntegrationRequestMailAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'resend_ejar_mail';
    }
    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('Resend Mail'));
        $this->color('warning');
        $this->icon('heroicon-o-envelope');
        $this->visible(fn(Model $record) => (empty($record->client_id) || empty($record->client_secret)));
        $this->action(function (Model $record, EjarIntegrationService $ejarService) {
            $this->handleMailResend($record, $ejarService);
        });
        $this->requiresConfirmation();
        $this->modalHeading(__('Request Ejar Integration Mail'));
        $this->modalDescription(__('Are you sure you want to resend integration request mail to Ejar?'));
        $this->modalSubmitActionLabel(__('Yes, resend it'));
    }

    protected function handleMailResend(Model $record, EjarIntegrationService $ejarService): void
    {
        $response = $ejarService->sendRequestEjarIntegration($record->company);

        if ($response['status']) {
            Notification::make()
                ->success()
                ->title(__("Mail of Ejar integration request sent successfully"))
                ->icon('heroicon-o-check-circle')
                ->iconColor('success')
                ->duration(5000)
                ->send();

            return;
        }
        Notification::make()
            ->danger()
            ->title(__('Could not resend integration request to Ejar'))
            ->body($response['message'])
            ->icon('heroicon-o-x-circle')
            ->iconColor('danger')
            ->persistent()
            ->send();
    }
}
