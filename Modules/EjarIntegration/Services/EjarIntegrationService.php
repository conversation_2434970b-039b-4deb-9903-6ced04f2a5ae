<?php

namespace Modules\EjarIntegration\Services;

use Illuminate\Support\Facades\Mail;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\Mail\EjarIntegrationRequest;

class EjarIntegrationService
{
    public $response = ['status' => false, 'message' => null];
    public function sendRequestEjarIntegration(Company $company): array
    {
        try {
            // Validate required company data
            $this->validateCompanyData($company);
            // Send email
            Mail::send(new EjarIntegrationRequest($company));
            //update response
            $this->response['status'] = true;
        } catch (\Exception $e) {
            $this->response['message'] = $e->getMessage();
        }

        return $this->response;
    }

    public function validateCompanyData(Company $company): void
    {
        if (empty($company->name) || empty($company->comp_cr_number) || empty($company->comp_unified_number)) {
            throw new \Exception(__('Please verify your ejar integration data such as company name, CR number and company unified number'));
        }
    }
}
