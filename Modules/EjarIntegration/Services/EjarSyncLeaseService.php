<?php
namespace Modules\EjarIntegration\Services;

use App\Helpers\LeaseSettingHelper;
use Modules\EjarIntegration\app\Helpers\EjarHttpHelper;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseMember;
use Modules\Lease\app\Models\LeaseTerm;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Lease\Enums\LeasePaymentEnum;
use Modules\Lease\Enums\LeaseTypesEnum;

class EjarSyncLeaseService
{
    use EjarHttpHelper;
    private $clientId;
    private $clientSecret;
    private $base_url;
    private $response;

    public function __construct()
    {
        $this->clientId = 'bf46c559a5a5e5a070102a189f11d35a';
        $this->clientSecret = '3801552dce97fd68240502c2f82e54bd';
        $this->base_url = 'https://test.kera.sa/nhc/uat/v1/ejar/ecrs/';

        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1"
        ]);
    }

    public function initializeLease(Lease $lease)
    {
        $this->response['status'] = false;
        $url = $this->base_url . 'PostContract';

        $payload = [
            'data' => [
                'attributes' => [
                    'contract_start_date' => $lease->start_date,
                    'contract_end_date' => $lease->end_date,
                    'contract_type' => $lease->lease_type,
                    'custom_terms_accepted' => true,
                ]
            ]
        ];

        // Set headers
        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1",
            "Accept" => "application/json",
            "Content-Type" => "application/json"
        ]);

        // Make the API request using the helper method
        $this->httpResponse = $this->post($url, $payload);

        if ($this->httpResponse['code'] === 201) {
            $result = json_decode($this->httpResponse['data']);
            $lease->update([
                'ejar_uuid' => $result->Body->data->id,
            ]);
        }

        return $this->httpResponse;
    }

    public function attachUnitsToLease(Lease $lease, array $units)
    {
        $unit = $lease->units->first();

        $payload = [
            'data' => [
                'contract_property' => [
                    'id' => $unit->parent->ejar_uuid,
                    'contract_units' => [
                        ['id' => $unit->ejar_uuid]
                    ]
                ]
            ]
        ];

        // Set headers
        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1",
            "Accept" => "application/json",
            "Content-Type" => "application/json"
        ]);

        // Make API request using the helper method
        $url = $this->base_url .'PostContractUnit?ContractId=' . $lease->ejar_uuid;
        $res = $this->post($url, $payload);

        if (isset($res['data']) && is_string($res['data'])) {
            $res['data'] = json_decode($res['data'], true);
        }

        if ($this->httpResponse['code'] === 201) {
            $result = json_decode($this->httpResponse['data']);
            $ejarId = $result->Body->data[0]->id;
            $lease->units()->updateExistingPivot($unit->id, ['ejar_id' => $ejarId]);

        }
        $this->httpResponse = $res;
        return $this->httpResponse;
    }

    public function checkEjarUsers($users)
    {
        $results = [
            'all_verified' => true,
            'users' => [],
            'error' => null
        ];

        try {
            foreach ($users as $user) 
            {
                $member = $user->member;
                switch($user->member_type)
                {
                    case LeaseMemberTypesEnum::INDIVIDUAL_TYPE:
                        $payload = [
                            'id_number' => $member->national_id,
                            'id_type' => 'national_id'
                        ];
                        $url = $this->base_url . 'GetIndividualEntity';
                        $response = $this->get($url, $payload);
                        
                        // Decode response data
                        $responseBody = json_decode($response['data'], true);
                        
                        // Extract verification status
                        $userVerified = $responseBody['Body'] !== null && $responseBody['Body']['data']['attributes']['verification_status'] === 'verification_succeed';
                        
                        if (!$userVerified) {
                            $results['all_verified'] = false;
                        }
                        $results['users'][]=$response;
                        // Save ejar_id
                        $member->ejar_uuid = $responseBody['Body']['data']['id'] ?? null;                   
                        $member->save();
                        break;
                    case LeaseMemberTypesEnum::ORGANIZATION_TYPE:
                        $payload = [
                            'registration_number' => $member->registration_number,
                            'registration_date' => $member->registration_date,
                        ];
                        $url = $this->base_url . 'GetOrganizationEntity';
                        $response = $this->get($url, $payload);
                        // Decode response data
                        $responseBody = json_decode($response['data'], true);
                        // Extract verification status
                        $userVerified = $responseBody['Body'] !== null && $responseBody['Body']['data']['attributes']['verification_status'] === 'verification_succeed';
                        if (!$userVerified) {
                            $results['all_verified'] = false;
                        }
                        $results['users'][]=$response;
                        // Save ejar_id
                        $member->ejar_uuid = $responseBody['Body']['data']['id'] ?? null;
                        $member->save();
                        break;
                }
            }
            return $results;

        } catch (\Exception $e) {
            return [
                'all_verified' => false,
                'users' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    public function attachTenantToLease(Lease $lease, $leaseMemebers)
    {
        foreach($leaseMemebers as $leaseMemeber)
        {
            $payload = [
                'data' => [
                    'type' => 'contract_parties',
                    'attributes' => [
                        'role' => ($leaseMemeber->member_role == LeaseMemberTypesEnum::TENANT_REPRESENTER) ? 'tenant_representative' : 'tenant'
                    ],
                    'relationships' => [
                        'entity' => [
                            'data' => [
                                'id' => $leaseMemeber->member->ejar_uuid,
                                'type' => $leaseMemeber->member_type == LeaseMemberTypesEnum::INDIVIDUAL_TYPE ? 'individual_entities' : 'organization_entities'
                            ]
                        ]
                    ]
                ]
            ];

            // Set headers
            $this->setHttpHeaders([
                "X-IBM-Client-Id" => $this->clientId,
                "X-IBM-Client-Secret" => $this->clientSecret,
                "RefId" => "1",
                "Accept" => "application/json",
                "Content-Type" => "application/json"
            ]);

            // Make API request using the helper method
            $url = $this->base_url . 'PostContractParty?ContractId=' . $lease->ejar_uuid;

            $res = $this->post($url, $payload);
            // Decode response data if it's a string
            if (isset($res['data']) && is_string($res['data'])) {
                $res['data'] = json_decode($res['data'], true);
            }

            $this->httpResponse = $res;
        }
        return $this->httpResponse;
    }

    public function attachFinancialDataToLease(Lease $lease)
    {
        $role = $lease->commission !== null ?  LeaseMember::where('member_id' , $lease->commission->commission_paid_by)->pluck('member_role')->first() : null;

        $payload = [
            'data' => [
                'type' => 'financial_information',
                'attributes' => [
                    'security_deposit_required' => $lease->insurance_amount > 0 ? true : false,
                    'security_deposit' => [
                        'amount' => $lease->insurance_amount > 0 ? (int) $lease->insurance_amount : null,
                        'currency' => 'SAR'
                    ],
                    'retainer_fee_required' => false,
                    'retainer_fee' => [
                        'amount' => 0,
                        'currency' => 'SAR'
                    ],
                    'late_fees_charged_required' => $lease->daily_penalty > 0 ? true : false,
                    'late_fees_charged' => [
                        'amount' => $lease->daily_penalty > 0 ? $lease->daily_penalty : null,
                        'currency' => 'SAR'
                    ],
                    'brokerage_fee_required' => $lease->commission !== null ? true : false,
                    'brokerage_fee' => [
                        'amount' => $lease->commission !== null ? (int)$lease->commission->commission_amount : 0,
                        'currency' => 'SAR'
                    ],
                    'utilities_and_services_payment_type' => 'monthly_fixed',
                    'iban_number' => '************************',
                    'iban_belong_to' => 'lessor',
                    'engineering_supervision_required' => $lease->lease_type == LeaseTypesEnum::COMMERCIAL ? true : false,
                    'engineering_supervision' => [
                        'amount' => $lease->lease_type == LeaseTypesEnum::COMMERCIAL ? $lease->commercial_meta_data->engineering_supervision : 0,
                        'currency' => 'SAR'
                    ],
                    'unit_finishing_required' => $lease->lease_type == LeaseTypesEnum::COMMERCIAL ? true : false,
                    'unit_finishing' => [
                        'amount' => $lease->lease_type == LeaseTypesEnum::COMMERCIAL ? $lease->commercial_meta_data->unit_finishing : 0,
                        'currency' => 'SAR'
                    ],
                    'waste_removal_required' => $lease->lease_type == LeaseTypesEnum::COMMERCIAL ? true : false,
                    'waste_removal' => [
                        'amount' => $lease->lease_type == LeaseTypesEnum::COMMERCIAL ? $lease->commercial_meta_data->waste_removal : 0,
                        'currency' => 'SAR'
                    ],
                    'brokerage_fee_due_date' => '2025-06-01',
                    'brokerage_fee_paid_by' => $role,
                ]
            ]
        ];
       // Set headers
        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1",
            "Accept" => "application/json",
            "Content-Type" => "application/json"
        ]);

        // API URL (Now using POST)
        $url = $this->base_url . 'PostContractFinancialInformation?ContractId=' . $lease->ejar_uuid;

        // Make API request using POST method
        $res = $this->postJson($url, $payload);

        // Decode response data if it's a string
        if (isset($res['data']) && is_string($res['data'])) {
            $res['data'] = json_decode($res['data'], true);
        }

        $this->httpResponse = $res;
        return $this->httpResponse;
    }

    public function attachLeaseUnitServices(Lease $lease)
    {
        $allowedKeys = [
            'water',
            'gas',
            'electricity',
            'parking_space',
            'internet',
            'gym',
            'swimming_pool'
        ];

        $leaseUnit = $lease->leaseUnits->first();
        $unitServices = $leaseUnit->AllleaseUnitServices()
            ->whereHas('service', function ($query) use ($allowedKeys) {
                $query->whereIn('key', $allowedKeys);
            })->where('ejar_id' , null)->get();
        $leaseUnitEjarId = $leaseUnit->ejar_id;

        // Set API headers
        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1",
            "Accept" => "application/json",
            "Content-Type" => "application/json"
        ]);

        // Loop through each unit service and send a separate request
        foreach ($unitServices as $unitService) {
            // Construct the payload for each service
            $attributes = [
                "utility_service_type" => $unitService->service->key,
                "to_be_paid_by" => $unitService->to_be_paid_by,
                "number_of_services" => $unitService->number_of_services ?? 1,
                "meter_type"=> "shared_meter",
                "selected" => true,
                "to_be_paid_at" => $unitService->to_be_paid_amount ?? 0,
            ];

            // Only include meter_current_reading if to_be_paid_by is NOT 'fixed_value'
            if ($unitService->to_be_paid_by !== 'fixed_fee') {
                $attributes["meter_current_reading"] = $unitService->meter_current_reading;
            }

            if ($unitService->service->key === 'electricity') {
                $attributes["electricity_premise_id"] = 1111111111;
            }

            // Use attributes inside the payload
            $payload = [
                "data" => [
                    "type"=> "contract_unit_services",
                    "attributes" => $attributes
                ]
            ];
            // Construct the API endpoint
            $url = $this->base_url . "PostContractUnitService?" . http_build_query([
                'ContractId' => $lease->ejar_uuid,
                'UnitId' => $leaseUnitEjarId
            ]);

            // Send the request to the API
            $response = $this->postJson($url, $payload);

            // Decode response if needed
            if (isset($response['data']) && is_string($response['data'])) {
                $response['data'] = json_decode($response['data'], true);
            }

            if ($response['code'] === 201) {
                $ejarId = $response['data']['Body']['data']['id'];
                $unitService->update(['ejar_id' => $ejarId]);
            }
        }
        // Store the final response
        $this->httpResponse = $response;
        return $this->httpResponse;
    }

    public function insertRentalFees(Lease $lease)
    {
        // Mapping database values to integration values
        $paymentTypeMapping = [
            'monthly' => 'monthly',
            'quarterly' => 'quarterly',
            'half_annually' => 'bi-annual',
            'annually' => 'annual',
        ];

        // Get the correct integration value
        $integrationPaymentType = $paymentTypeMapping[$lease->payment_repeated_type] ?? 'one_time_pay';

        // Construct the payload
        $payload = [
            'data' => [
                'type' => 'rental_fees',
                'attributes' => [
                    'total_units_rent' => [
                        'amount' => (int)$lease->rent_amount,
                        'currency' => 'SAR'
                    ],
                    'rent_type' => 'for_all_units',
                    'billing_type' => $lease->payment_type == LeasePaymentEnum::REPEATED ? $integrationPaymentType : 'one_time_pay',
                    'utilities_and_services_required' => true
                ]
            ]
        ];

        // Set headers
        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1",
            "Accept" => "application/json",
            "Content-Type" => "application/json"
        ]);

        $url = $this->base_url . 'PostContractRentalFee?ContractId=' . $lease->ejar_uuid;

        $res = $this->postJson($url, $payload);

        if (isset($res['data']) && is_string($res['data'])) {
            $res['data'] = json_decode($res['data'], true);
        }

        $this->httpResponse = $res;
        return $this->httpResponse;
    }


    public function syncTerms(Lease $lease)
    {
        // Construct the payload
        $payload = [
            'data' => [
                'type' => 'contract_terms',
                'attributes' => [
                    'term_type' => 'residential',
                    'ejar_fees_paid_by' => 'brokerage_office',
                ],
                'relationships' => [
                    'contract_term_answers' => [
                        'data' => [
                            [
                                'type' => 'contract_term_answers',
                                'attributes' => [
                                    'term_template_id' => 26,
                                    'term_template_key' => 'residential_auto_renewal',
                                    'enabled' => $lease->auto_renewal == LeaseAutoRenewalEnum::ON ? true : false,
                                    'answers' => [
                                        'unit_opening_period' => LeaseSettingHelper::getLeaseSettingValue('renewal_notice_period'),
                                        'notification_for_termination_period' =>  LeaseSettingHelper::getLeaseSettingValue('days_to_remind_before_renewal')
                                    ],
                                ]
                            ],
                            [
                                'type' => 'contract_term_answers',
                                'attributes' => [
                                    'term_template_id' => 36,
                                    'term_template_key' => 'tenant_can_sublease',
                                    'enabled' => $this->getLeaseTermValue('is_sublease') == 1 ? true : false
                                ]
                            ],
                            [
                                'type' => 'contract_term_answers',
                                'attributes' => [
                                    'term_template_id' => 39,
                                    'term_template_key' => 'residential_followup_with_authorities',
                                    'enabled' => $this->getLeaseTermValue('is_reviewing_government')== 1 ? true : false
                                ]
                            ],
                            [
                                'type' => 'contract_term_answers',
                                'attributes' => [
                                    'term_template_id' => 40,
                                    'term_template_key' => 'renovations_and_improvements_rental_unit',
                                    'enabled' => $this->getLeaseTermValue('is_repairs_improvements')== 1 ? true : false
                                ]
                            ],
                            [
                                'type' => 'contract_term_answers',
                                'attributes' => [
                                    'term_template_id' => 41,
                                    'term_template_key' => 'modification_rental_unit',
                                    'enabled' => $this->getLeaseTermValue('is_modify_rental_unit')== 1 ? true : false
                                ]
                            ],
                            [
                                'type' => 'contract_term_answers',
                                'attributes' => [
                                    'term_template_id' => 42,
                                    'term_template_key' => 'governing_law_and_dispute_resolution',
                                    'enabled' => true,
                                    'answers'  =>
                                    [
                                        'governing_law_and_dispute_resolution_option' => $this->getLeaseTermValue('governing_law') == 'JudicialAuthority' ? 'competent_judicial_authority' : 'real_estate_arbitration',
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        // Set headers
        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1",
            "Accept" => "application/json",
            "Content-Type" => "application/json"
        ]);

        $url = $this->base_url . 'PostContractTerms?ContractId=' . $lease->ejar_uuid;
        $res = $this->postJson($url, $payload);

        if (isset($res['data']) && is_string($res['data'])) {
            $res['data'] = json_decode($res['data'], true);
        }
        $this->httpResponse = $res;
        return $this->httpResponse;
    }

    private function getLeaseTermValue($key)
    {
        return LeaseTerm::where('key' , $key)->select('value')->first()->value;
    }

    public function syncCustomTerms(Lease $lease)
    {
        $customTerms = $lease->leaseTerms->where('key', 'additional_terms');

        if ($customTerms->isEmpty()) {
            return [
                'status' => true,
                'message' => 'No custom terms found to sync.',
                'data' => null,
                'code' => 201
            ];
        }

        foreach ($customTerms as $terms) {
            // Construct the payload
            $payload = [
                'data' => [
                    'type' => 'custom_terms',
                    'attributes' => [
                        'content' => $terms->value,  // Custom term content
                        'status' => '',  // API request requires this field, so keeping it empty
                    ]
                ]
            ];

            // Set headers
            $this->setHttpHeaders([
                "X-IBM-Client-Id" => $this->clientId,
                "X-IBM-Client-Secret" => $this->clientSecret,
                "RefId" => "1",
                "Accept" => "application/json",
                "Content-Type" => "application/json"
            ]);

            // Construct the API URL
            $url = $this->base_url . 'PostContractCustomTerms?ContractId=' . $lease->ejar_uuid;

            // Send request
            $res = $this->postJson($url, $payload);

            // Handle response
            if (isset($res['data']) && is_string($res['data'])) {
                $res['data'] = json_decode($res['data'], true);
            }
        }

        $this->httpResponse = $res ?? null;
        return $this->httpResponse;
    }

    public function submitContract(Lease $lease)
    {
        // Construct the API URL
        $url = $this->base_url . 'PatchContractSubmit?ContractId=' .$lease->ejar_uuid ;

        // Set headers
        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1",
            "Accept" => "application/json",
            "Content-Type" => "application/json"
        ]);

        // Send PATCH request with empty payload (since no attributes are required)
        $res = $this->patch($url, []);

        // Handle response
        if (isset($res['data']) && is_string($res['data'])) {
            $res['data'] = json_decode($res['data'], true);
        }

        $this->httpResponse = $res;
        return $this->httpResponse;
    }
}
