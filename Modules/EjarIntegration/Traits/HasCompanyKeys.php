<?php
namespace Modules\EjarIntegration\Traits;

use App\Models\User;
use Modules\Company\app\Models\Company;
use Modules\Property\app\Models\Property;

trait HasCompanyKeys
{
    /**
     * @throws \Exception
     */
    public function setCompanyKeysByProperty(Property $property) {
        $companyOfProperty = $property->company;
        if ($companyOfProperty && $companyOfProperty->ejarKeys) {
            $this->clientId = $companyOfProperty?->ejarKeys?->client_id ?? ''; //todo handle unauthorized exception
            $this->clientSecret = $companyOfProperty?->ejarKeys?->client_secret ?? '';
        } else {
            throw new \Exception(__("Invalid company keys for integration."));
        }
    }

    /**
     * @throws \Exception
     */
    public function setCompanyKeysByOwner(User $user) {
        $userCompany = $user->company;
        if ($userCompany && $userCompany->ejarKeys) {
            $this->clientId = $userCompany->ejarKeys->client_id; //todo handle unauthorized exception
            $this->clientSecret = $userCompany->ejarKeys->client_secret;
        } else {
            throw new \Exception(__("Invalid company keys for integration."));
        }
    }

    /**
     * @throws \Exception
     */
    public function setCompanyKeysByCompany(Company $company) {
        if ($company->ejarKeys) {
            $this->clientId = $company->ejarKeys->client_id; //todo handle unauthorized exception
            $this->clientSecret = $company->ejarKeys->client_secret;
        } else {
            throw new \Exception(__("Invalid company keys for integration."));
        }
    }
}
