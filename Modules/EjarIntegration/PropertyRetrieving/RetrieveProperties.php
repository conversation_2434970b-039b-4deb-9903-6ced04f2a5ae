<?php
namespace Modules\EjarIntegration\PropertyRetrieving;

use Modules\EjarIntegration\Interfaces\RetrievePropertiesAbstract;

Class RetrieveProperties extends RetrievePropertiesAbstract
{
    public array $properties = [];
    public array $units = [];
    public array $meta = [];
    public array $params = [];

    /**
     * @throws \Exception
     */
    public function retrieveProperties(): void
    {
        try {
            $url = $this->base_url . 'GetProperties';
            $this->get($url, $this->params);
            $res = json_decode($this->httpResponse['data'], true);

            if ($this->httpResponse['code'] == 200) {
                $this->properties = $res['Body']['data'];
                if (isset($res['Body']['included'])) {
                    $this->units = $res['Body']['included'];
                }
                $this->meta = $res['Body']['meta'];
            } else {
                if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                    $msg = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                } else {
                    $msg = __("Something went wrong, please try again later.");
                }
                throw new \Exception($msg);
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function setParams(array $data): void
    {
        $this->params = $data;
    }
}
