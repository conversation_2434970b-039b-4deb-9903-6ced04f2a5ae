<?php

namespace Modules\Notification\Services;

use Khaleds\Notifications\Models\UserToken;
use Modules\Account\app\Models\Account;

class NotificationService
{


    public function __construct(
        private UserToken $model
    )
    {}

    public function update(string $token,int $id):void{
        $deviceToken =    $this->model->where('model_id',$id)->first();
        if($deviceToken){
            $deviceToken->update([
                'provider_token' =>  $token
            ]);

        }
    }


    public function store(string $token,int $id):void{

        $this->model->updateOrCreate(
            [
                "model_id"=>$id,
            ],
            [
                "model_type"=>Account::class,
                "model_id"=>$id,
                "provider"=>"fcm-api ",
                "provider_token"=>$token
            ]);


    }

    public function delete( int $id):void{
        $this->model->where('model_id',$id)->delete();
    }

    public function isChecked(int $id,string $class){
        $this->model->where('model_id',$id)->delete();

    }
}
