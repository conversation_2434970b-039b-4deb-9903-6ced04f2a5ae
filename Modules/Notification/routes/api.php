<?php

use Illuminate\Support\Facades\Route;
use Modules\Notification\app\Http\Controllers\api\UserNotificationController;
/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::post('/notifications/notify-by-mail', [UserNotificationController::class, 'notifyByMail']); //test notification by mail

Route::middleware(['auth:sanctum'])->group(function () {
    Route::prefix('notifications')->group(function () {
        Route::get('/', [UserNotificationController::class, 'index']);
        Route::post('/clear', [UserNotificationController::class, 'clear']);
        Route::delete('/{id}/delete', [UserNotificationController::class, 'destroy']);
        Route::post('/{id}/read', [UserNotificationController::class, 'markAsRead']);
        Route::post('/toggle', [UserNotificationController::class, 'setting']);
    });
});
