<?php

namespace Modules\Notification\app\Observers;

use App\Models\User;
use Filament\Notifications\Notification;
use Khaleds\Notifications\Models\UserNotification;

class UserNotificationObserver
{
    /**
     * Handle the UserNotification "created" event.
     */
    public function created(UserNotification $usernotification): void
    {
        if ($usernotification->model_type == User::class) {
            Notification::make()
                ->title($usernotification->title)
                ->body($usernotification->description)
                ->status($usernotification->type ?? 'info')
                ->date($usernotification->created_at)
                ->sendToDatabase(User::find($usernotification->model_id))
                ->toDatabase();
        }
    }

    /**
     * Handle the UserNotification "updated" event.
     */
    public function updated(UserNotification $usernotification): void
    {
        //
    }

    /**
     * Handle the UserNotification "deleted" event.
     */
    public function deleted(UserNotification $usernotification): void
    {
        //
    }

    /**
     * Handle the UserNotification "restored" event.
     */
    public function restored(UserNotification $usernotification): void
    {
        //
    }

    /**
     * Handle the UserNotification "force deleted" event.
     */
    public function forceDeleted(UserNotification $usernotification): void
    {
        //
    }
}
