<?php

namespace Modules\Notification\app\Http\Controllers\api;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Khaleds\Notifications\Models\UserNotification;
use Khaleds\Notifications\Models\UserToken;
use Khaleds\Notifications\Services\SendNotification;
use Khaleds\Shared\Helpers\ApiResponse;
use Modules\Account\app\Models\Account;
use Modules\Notification\app\Http\Requests\NotificationCollection;
use Modules\Notification\app\Models\NotificationsTemplate;


class UserNotificationController
{


    public function index(Request $request)
    {
        //filter by read
        $searchItems['model_type'] =Account::class;
        $searchItems['model_id'] =auth()->user()->id;


        if ($request->has('is_read'))
            $searchItems['is_read']=$request->input('is_read');

        $limit = $request->limit ?? 10;

        $notifications = UserNotification::where($searchItems)
            ->orderBy('id','desc')
            ->paginate($limit);

        foreach ($notifications as $item) {
            $item->icon = $item->template ? count($item->template->getMedia('image')) ? $item->template->getMedia('image')->first()->getUrl() : null : null;
//            $item->is_read = UserReadNotification::where('model_type', Account::class)
//                ->where('model_id', $request->user()->id)
//                ->where('notification_id', $item->id)->first() ? 1 : 0;
            unset($item->template);
        }

        return ApiResponse::dataWithMeta(new NotificationCollection($notifications),__('all notifications'));

    }

    public function clear()
    {

        UserNotification::where('model_type', Account::class)->where('model_id', auth()->user()->id)->delete();

        return response()->json([
            "success" => true,
            "message" => __('Notifications Has Been Cleared'),
            "data" => []
        ]);
    }

    public function destroy(Request $request, $id)
    {
        $notification = UserNotification::where([
            'id' => $id,
            'model_id' => auth()->user()->id,
        ])->first();
        if($notification){
            $notification->delete();
            return response()->json([
                "success" => true,
                "message" => __('Notifications Has Been Deleted'),
                "data" => []
            ]);
        }else{
            return response()->json([
                "success" => true,
                "message" => __('There is no Notifications here to deleted'),
                "data" => []
            ]);
        }


    }

    public function markAsRead(Request $request, $id)
    {
        $notifications = UserNotification::find($id);

        if ($notifications) {

//            $notifications = new UserReadNotification();
//            $notifications->model_type = "Modules\Accounts\Entities\Account";
//            $notifications->model_id = $request->user()->id;
//            $notifications->notification_id = $id;
//            $notifications->read = true;
//            $notifications->open = true;
            $notifications->is_read = 1;
            $notifications->save();

            return response()->json([
                "success" => true,
                "message" => __('Notifications Has Been Marked As Read'),
                "data" => []
            ]);
        }
        return response()->json([
            "success" => false,
            "message" => __('Notifications Not Found'),
            "data" => []
        ], 404);
    }

    public function setting(Request $request)
    {
        if ($request->has('token')) {
            $token = new UserToken();
            $token->model_type = Account::class;
            $token->model_id = $request->user()->id;
            $token->provider = "fcm-api";
            $token->provider_token = $request->get('token');
            $token->save();

            return response()->json([
                "success" => true,
                "message" => __('Your Notifications Has Been On'),
                "body" => []
            ]);
        }

        UserToken::where('model_type', Account::class)
            ->where('provider', 'fcm-api')
            ->where('model_id', $request->user()->id)
            ->delete();

        return response()->json([
            "success" => true,
            "message" => __('Your Notifications Has Been Off'),
            "body" => []
        ]);


    }

    public function update($id){

        UserNotification::where('id', $id)->update([
            "is_read" => 1
        ]);

        return redirect()->back();
    }

    public function sendView(Request $request){
        $customer_id = null;
        if($request->has('customer_id') && !empty($request->get('customer_id'))){
            $customer_id = $request->get('customer_id');
        }
        return view('notification::index', [
//            "templates" => NotificationsTemplate::where('action', 'manual')->get(),
            "customer_id" => $customer_id
        ]);
    }

    public function send(Request $request){

        if($request->has('use_template') && $request->get('use_template')){
            $request->validate([
                "template_id" => "required|exists:notifications_templates,id",
                "privacy" => "required",
                "providers" => "required|array",
                "providers.*" => "required",
            ]);
            $template = NotificationsTemplate::find($request->get('template_id'));

            if($request->get('privacy') === 'customer'){
                SendNotification::make($request->get('providers'))
                    ->template($template->key)
                    ->model(Account::class)
                    ->id($request->get('account_id'))
                    ->icon('bx bx-user')
                    ->url(url('/'))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
            else if($request->get('privacy') === 'group'){
                $group = $request->get('group_id');
                $accounts = Account::whereHas('groups', function ($q) use ($group){
                    $q->where('id', $group);
                })->get();

                foreach($accounts as $account){
                    SendNotification::make($request->get('providers'))
                        ->template($template->key)
                        ->model(Account::class)
                        ->id($account->id)
                        ->icon('bx bx-user')
                        ->url(url('/'))
                        ->privacy('private')
                        ->database(true)
                        ->fire();
                }
            }
            else {
                SendNotification::make($request->get('providers'))
                    ->template($template->key)
                    ->model(Account::class)
                    ->privacy('public')
                    ->database(true)
                    ->icon('bx bx-user')
                    ->url(url('/'))
                    ->fire();
            }

        }
        else {
            $request->validate([
                "privacy" => "required",
                "providers" => "required|array",
                "providers.*" => "required",
                "image" => "required|file|max:2000",
                "title" => "required|string|max:255",
                "body" => "required|string|max:255",
            ]);

            if($request->hasFile('image')){
                $path = $request->file('image')->store('/notifications/images');
            }
            else {
                $path = null;
            }

            if($request->get('privacy') === 'customer'){
                SendNotification::make($request->get('providers'))
                    ->title($request->get('title'))
                    ->message($request->get('body'))
                    ->type($request->get('type'))
                    ->database(true)
                    ->image($path)
                    ->model(Account::class)
                    ->id($request->get('account_id'))
                    ->privacy('private')
                    ->icon('bx bx-user')
                    ->url(url('/'))
                    ->fire();
            }
            else if($request->get('privacy') === 'group'){
                $group = $request->get('group_id');
                $accounts = Account::whereHas('groups', function ($q) use ($group){
                    $q->where('id', $group);
                })->get();

                foreach($accounts as $account){
                    SendNotification::make($request->get('providers'))
                        ->title($request->get('title'))
                        ->message($request->get('body'))
                        ->type($request->get('type'))
                        ->image($path)
                        ->model(Account::class)
                        ->database(true)
                        ->id($account->id)
                        ->icon('bx bx-user')
                        ->url(url('/'))
                        ->privacy('private')
                        ->fire();
                }
            }
            else {
                SendNotification::make($request->get('providers'))
                    ->title($request->get('title'))
                    ->message($request->get('body'))
                    ->type($request->get('type'))
                    ->image($path)
                    ->model(Account::class)
                    ->icon('bx bx-user')
                    ->url(url('/'))
                    ->database(true)
                    ->privacy('public')
                    ->fire();
            }
        }


        Toast::title(trans('tomato-notifications::global.templates.send_message'))->success()->autoDismiss(2);
        return back();
    }

    public function notifyByMail(Request $request)
    {
        try {
            $mail_address = $request->get('email');
            $user = User::where(['email' => $mail_address])->first();
            if (empty($user)) {
                throw new \Exception('User not found');
            }
            $template = NotificationsTemplate::where(['key' => 'payment_success'])->first();
            if ($template) {
                SendNotification::make(['email'])
                    ->template($template->key)
                    ->model(User::class)
                    ->id($user->id)
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
            Log::channel('mail')->info('Email notification sent successfully to: ' . $mail_address);

            return response()->json([
                "success" => true,
                "message" => __('ok'),
                "data" => []
            ]);
        } catch (\Exception $e) {
            Log::channel('mail')->info('Email notification failed to: ' . $mail_address . ': ' . $e->getMessage());

            return response()->json([
                "success" => false,
                "message" => $e->getMessage(),
                "data" => $e
            ]);
        }
    }
}
