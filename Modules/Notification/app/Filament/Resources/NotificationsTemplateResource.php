<?php

namespace Modules\Notification\app\Filament\Resources;

use App\Models\User;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\ActionGroup;
use Guava\FilamentIconPicker\Forms\IconPicker;
use Filament\Actions\LocaleSwitcher;
use Filament\Notifications\Notification;
use Modules\Notification\app\Filament\Resources\NotificationsTemplateResource\Pages;
use Modules\Notification\app\Filament\Resources\NotificationsTemplateResource\RelationManagers;

//use TomatoPHP\FilamentAlerts\Models\NotificationsTemplate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Resources\Concerns\Translatable;
use Khaleds\Notifications\Services\SendNotification;
use Filament\Tables\Columns\IconColumn;
use Modules\Notification\app\Models\NotificationsTemplate;


class NotificationsTemplateResource extends Resource
{
    use Translatable;

    protected static ?string $model = NotificationsTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-bookmark';

//    protected static ?int $navigationSort = 1;

    //    public static function shouldRegisterNavigation(): bool
    //    {
    //        return false; // TODO: Change the autogenerated stub
    //    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(['default' => 3])
                    ->schema([
                        Forms\Components\SpatieMediaLibraryFileUpload::make('image')
                            ->label(__('Image'))
                            ->collection('image')
                            ->maxFiles(1)
                            ->maxWidth(1024)
                            ->maxSize(5120)
                            ->acceptedFileTypes(['image/*'])
                            ->columnSpan(3),
                        Forms\Components\TextInput::make('name')
                            ->label(__('Name'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('key')
                            ->label(__('key'))
                            ->unique(table: 'notifications_templates', column: 'key', ignoreRecord: true)
                            ->required()
                            ->disabled()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('url')
                            ->label(__('Url'))
                            ->url()
                            ->maxLength(255),
                        Forms\Components\Select::make('type')
                            ->label(__('Type'))
                            ->options(collect(config('filament-alerts.types'))->pluck('name', 'id')->toArray())
                            ->default('success'),
                        Forms\Components\Select::make('providers')
                            ->label(__('Providers'))
                            ->multiple()
                            ->options(collect(config('filament-alerts.providers'))->pluck('name', 'id')->toArray()),
                        Forms\Components\Select::make('action')
                            ->label(__('Methods'))
                            ->options([
                                'manual' => __('Manual'),
                                'system' => __('System'),
                            ])
                            ->default('manual'),
                        Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make(__('Arabic'))
                                    ->schema([
                                        TextInput::make('title.ar')
                                            ->label(__('Title'))
                                            ->placeholder(__('Title'))
                                            ->required(),
                                        MarkdownEditor::make('body.ar')
                                            ->label(__('Body'))
                                            ->placeholder(__('Body'))
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('English'))
                                    ->schema([
                                        TextInput::make('title.en')
                                            ->label(__('Title'))
                                            ->placeholder(__('Title'))
                                            ->required(),
                                        MarkdownEditor::make('body.en')
                                            ->label(__('Body'))
                                            ->placeholder(__('Body'))
                                            ->required(),
                                    ]),

                            ])
                            ->columnSpan(3)
                            ->extraAttributes(['style' => 'min-height: 100%;']),

//                        IconPicker::make('icon')
//                            ->label(__('Icon'))
//                            ->columnSpan(3)
//                            ->default('heroicon-o-check-circle'),

                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('key')
                    ->label(__('key'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->label(__('Title'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('action')
                    ->label(__('Methods'))
                    ->searchable()
                    ->options([
                        'manual' => (__('templates form manual')),
                        'system' => (__('templates form system')),
                    ]),
                Tables\Filters\SelectFilter::make('type')->label(__('Type'))
                    ->searchable()
                    ->options(collect(config('filament-alerts.types'))->pluck('name', 'id')->toArray()),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\Action::make(__('clone'))
                        ->requiresConfirmation()
                        ->action(function (NotificationsTemplate $record) {

                            NotificationsTemplate::create([
                                "name" => $record->name . " (Clone)",
                                "key" => $record->key . "-clone-" . time(),
                                "title" => $record->title . " (Clone)",
                                "body" => $record->body,
                                "url" => $record->url,
                                "icon" => $record->icon,
                                "type" => $record->type,
                                "providers" => $record->providers,
                                "action" => $record->action,
                            ]);

                            Notification::make()
                                ->title(__('clone notification'))
                                ->success()
                                ->send();
                        })
                        ->color('info')
                        ->icon('heroicon-o-document-duplicate'),
                    Tables\Actions\Action::make(__('try'))
                        ->requiresConfirmation()
                        ->action(function (NotificationsTemplate $record) {
                            $matchesTitle = array();
                            preg_match('/{.*?}/', $record->title, $matchesTitle);
                            $titleFill = [];
                            foreach ($matchesTitle as $titleItem) {
                                $titleFill[] = "test-title";
                            }
                            $matchesBody = array();
                            preg_match('/{.*?}/', $record->body, $matchesBody);
                            $titleBody = [];
                            foreach ($matchesBody as $bodyItem) {
                                $titleBody[] = "test-body";
                            }

                            try {
                                SendNotification::make($record->providers)
                                    ->template($record->key)
                                    ->findTitle($matchesTitle)
                                    ->replaceTitle($titleFill)
                                    ->findBody($matchesBody)
                                    ->replaceBody($titleBody)
                                    ->model(User::class)
                                    ->id(User::first()->id)
                                    ->privacy('private')
                                    ->fire();

                                Notification::make()
                                    ->title(trans('filament-alerts::messages.templates.actions.try-notification'))
                                    ->success()
                                    ->send();

                            } catch (\Exception $exception) {
                                Notification::make()
                                    ->title(trans('filament-alerts::messages.templates.actions.try-notification-danger'))
                                    ->danger()
                                    ->send();
                            }
                        })
                        ->color('success')
                        ->icon('heroicon-o-paper-airplane'),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotificationsTemplates::route('/'),
            'create' => Pages\CreateNotificationsTemplate::route('/create'),
            'edit' => Pages\EditNotificationsTemplate::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Notifications');
    }

    public static function getNavigationLabel(): string
    {
        return __('Notifications Templates');
    }

    public static function getBreadcrumb(): string
    {
        return __('Notifications Templates');
    }

    public static function getModelLabel(): string
    {
        return __('NotificationsTemplate');
    }

    public static function getPluralModelLabel(): string
    {
        return __('NotificationsTemplate');
    }
}
