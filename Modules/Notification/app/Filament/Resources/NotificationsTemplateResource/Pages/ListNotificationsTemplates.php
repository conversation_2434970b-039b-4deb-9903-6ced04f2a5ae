<?php

namespace Modules\Notification\app\Filament\Resources\NotificationsTemplateResource\Pages;

use Filament\Pages\Actions\Action;
use Modules\Notification\app\Filament\Resources\NotificationsTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Notification\app\Filament\Resources\UserNotificationResource;

class ListNotificationsTemplates extends ListRecords
{
//    use ListRecords\Concerns\Translatable;


//    public ?string $activeLocale = null;

    protected static string $resource = NotificationsTemplateResource::class;

    public function getTitle():string
    {
        return (__('Messages Templates'));
    }

    public static function getNavigationLabel(): string
    {
        return (__('filament-alerts::messages.templates.title'));
    }


    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Messages Templates Create')),
            Actions\Action::make('notifications')
                ->action(fn()=> redirect()->to(UserNotificationResource::getUrl('index')))
                ->color('danger')
                ->label(__('Messages Back')),
//            Actions\LocaleSwitcher::make(),
        ];
    }
}
