<?php

namespace Modules\Notification\app\Filament\Resources\NotificationsTemplateResource\Pages;

use Modules\Notification\app\Filament\Resources\NotificationsTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateNotificationsTemplate extends CreateRecord
{
//    use CreateRecord\Concerns\Translatable;

    public ?string $activeLocale = null;

    protected static string $resource = NotificationsTemplateResource::class;

    public function getTitle():string
    {
        return (__('templates create'));
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
