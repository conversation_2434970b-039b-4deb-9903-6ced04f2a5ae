<?php

namespace Modules\Notification\app\Filament\Resources\NotificationsTemplateResource\Pages;

use Modules\Notification\app\Filament\Resources\NotificationsTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditNotificationsTemplate extends EditRecord
{
//    use EditRecord\Concerns\Translatable;

    public ?string $activeLocale = null;

    protected static string $resource = NotificationsTemplateResource::class;

    public function getTitle():string
    {
        return (__('templates edit'));
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\LocaleSwitcher::make(),
        ];
    }
}
