<?php

namespace Modules\Notification\app\Filament\Resources\UserNotificationResource\Pages;

use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use Khaleds\Notifications\Models\NotificationsLogs;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Modules\Notification\app\Filament\Resources\NotificationsLogsResource;
use Modules\Notification\app\Filament\Resources\NotificationsTemplateResource;
use Modules\Notification\app\Filament\Resources\UserNotificationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Khaleds\Notifications\Services\SendNotification;

class ManageUserNotifications extends ManageRecords
{
    protected static string $resource = UserNotificationResource::class;


    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add User Notification'))
                ->mutateFormDataUsing(function(array $data)
                    {
                        $template = NotificationsTemplate::find($data['template_id']);
                        if($template){
                            $data['title'] = $template->name;
                            $data['body'] = $template->body;
                            $data['icon'] = $template->icon;
                            $data['url'] = $template->url;
                            $data['type'] = $template->type;
                            $data['providers'] = $template->providers;
                            $data['created_by'] = auth()->user()->id;
                        }

                        return $data;
                    }
                )
                ->after(function($record){
                    SendNotification::make($record->template->providers)
                        ->title($record->template->title)
                        ->template($record->template->key)
                        ->database(false)
                        ->privacy($record->privacy)
                        ->model($record->model_type)
                        ->id($record->model_id)
                        ->fire();
                }),
            Action::make('templates')
                ->icon('heroicon-o-document-text')
                ->hiddenLabel()
                ->action(fn()=> redirect()->to(NotificationsTemplateResource::getUrl('index')))
                ->color('danger'),
            Action::make('logs')
                ->icon('heroicon-o-archive-box-arrow-down')
                ->hiddenLabel()
                ->action(fn()=> redirect()->to(NotificationsLogsResource::getUrl('index')))
                ->color('info'),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $template = NotificationsTemplate::find($data['template_id']);
        if($template){
            $data['title'] = $template->name;
            $data['body'] = $template->body;
            $data['icon'] = $template->icon;
            $data['url'] = $template->url;
            $data['type'] = $template->type;
            $data['providers'] = $template->providers;
        }

        return $data;
    }

}
