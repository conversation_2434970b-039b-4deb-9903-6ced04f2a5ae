<?php

namespace Modules\Notification\app\Filament\Resources\UserNotificationResource\Pages;

use Filament\Pages\Actions\Action;
use Modules\Notification\app\Filament\Resources\UserNotificationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserNotifications extends ListRecords
{
    protected static string $resource = UserNotificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Action::make('logs')->action(fn()=> redirect()->route('filament.admin.pages.settings-hub'))->color('info'),
        ];
    }
}
