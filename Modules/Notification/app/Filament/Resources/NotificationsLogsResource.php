<?php

namespace Modules\Notification\app\Filament\Resources;

use Modules\Notification\app\Filament\Resources\NotificationsLogsResource\Pages;
use Modules\Notification\app\Filament\Resources\NotificationsLogsResource\RelationManagers;
//use TomatoPHP\FilamentAlerts\Models\NotificationsLogs;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Modules\Notification\app\Models\NotificationsLogs;

class NotificationsLogsResource extends Resource
{
    protected static ?string $model = NotificationsLogs::class;

    protected static ?string $navigationIcon = 'heroicon-o-exclamation-circle';


    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('model.name')
                    ->label(__('Name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->label(__('Title'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('Type'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('provider')
                    ->label(__('Provider'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageNotificationsLogs::route('/')
        ];
    }
    public static function getNavigationGroup(): ?string
    {
        return __('Notifications');
    }
}
