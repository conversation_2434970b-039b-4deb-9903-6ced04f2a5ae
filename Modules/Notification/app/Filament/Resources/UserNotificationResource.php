<?php

namespace Modules\Notification\app\Filament\Resources;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Modules\Notification\app\Filament\Resources\UserNotificationResource\Pages;
use Guava\FilamentIconPicker\Tables\IconColumn;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Modules\Account\app\Models\Account;
use Modules\Notification\app\Filament\Resources\UserNotificationResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Modules\Notification\app\Models\UserNotification;

class UserNotificationResource extends Resource
{
    protected static ?string $model = UserNotification::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell';

    protected static ?int $navigationSort = 2;




    public static function form(Form $form): Form
    {
        return $form
            ->schema([
//                Forms\Components\Select::make('template_id')
//                    ->searchable()
//                    ->validationAttribute('template_id','required|exists:notifications_templates,id')
//                    ->label(trans('filament-alerts::messages.notifications.form.template'))
////                    ->options(
////                        NotificationsTemplate::pluck('name', 'id')->toArray()
////                    )
//                    ->relationship('template', 'name')
//                    ->required(),
                Forms\Components\Select::make('template_id')
                    ->label(__('Template'))
                    ->relationship('template', 'name')
                    ->required(),
                Forms\Components\Select::make(__('privacy'))
                    ->searchable()
                    ->options([
                        'public' => __('Public'),
                        'private' => __('Private'),
                    ])
                    ->live()
                    ->required()
                    ->default('public'),
                Forms\Components\Select::make('model_type')
                    ->label(__('Model Type'))
                    ->searchable()
                    ->options(config('filament-alerts.models'))
                    ->required()
                    ->live(),
                Forms\Components\Select::make('model_id')
                    ->searchable()
                    ->hidden(fn (Forms\Get $get): bool => $get('privacy') !== 'private')
                    ->options(fn (Forms\Get $get) =>
                    $get('model_type')
                        ? ($get('model_type') == User::class
                        ? $get('model_type')::pluck('name', 'id')->toArray()
                        : ($get('model_type') == Account::class
                            ? $get('model_type')::pluck('first_name', 'id')->toArray()
                            : []))
                        : [])
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {

        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query
                ->where(['model_type'=>auth()->user()::class])
                ->where('created_at','>=',auth()->user()->created_at)
                ->orWhere(['model_id'=> auth()->id(),'model_id'=> NULL])
            )->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('model.name')
                    ->label(__('Name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('template.name')
                    ->label(__('Template'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
            ])
            ->filters([
                //
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageUserNotifications::route('/'),
        ];
    }
    public static function getNavigationGroup(): ?string
    {
        return __('Notifications');
    }
    public static function getNavigationLabel(): string
    {
        return __("User Notifications");
    }
    public static function getBreadcrumb() : string
    {
        return __('User Notifications');
    }
    public static function getModelLabel(): string
    {
        return __('UserNotification');
    }

    public static function getPluralModelLabel(): string
    {
        return __('UserNotification');
    }

}
