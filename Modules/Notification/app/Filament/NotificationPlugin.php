<?php

namespace Modules\Notification\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Notification\app\Filament\Resources\NotificationsTemplateResource;
use Modules\Notification\app\Filament\Resources\NotificationsLogsResource;
use Modules\Notification\app\Filament\Resources\UserNotificationResource;

class NotificationPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return __('Notification');
    }

    public function getId(): string
    {
        return 'notification';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }

    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                UserNotificationResource::class,
                NotificationsTemplateResource::class,
                NotificationsLogsResource::class,
            ]);
    }
}
