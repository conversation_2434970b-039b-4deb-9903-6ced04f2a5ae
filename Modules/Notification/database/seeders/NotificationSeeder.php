<?php

namespace Modules\Notification\database\seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Khaleds\Notifications\Models\UserNotification;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (UserNotification::count() == 0){
            $sql = \Illuminate\Support\Facades\File::get(base_path('Modules/Notification/database/user_notifications.sql'));
            DB::connection()->getPdo()->exec($sql);
        }
    }
}
