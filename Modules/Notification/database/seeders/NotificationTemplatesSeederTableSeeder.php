<?php

namespace Modules\Notification\database\seeders;

use Illuminate\Database\Seeder;
use Khaleds\Notifications\Models\NotificationsTemplate;

class NotificationTemplatesSeederTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            //user & account
            [
                'name' => 'new user',
                'key' => 'send_user_credential',
                'icon' => 'bx bx-circle',
                'url' => url('/admin/login'),
                'body' => [
                    "ar" => "مرحباً {username}، نشكرك للانضمام إلى خدمتنا. فيما يلي بيانات الدخول وإعادة تعيين كلمة المرور الخاصة بك.<br><br>البريد الإلكتروني: {email}<br>كلمة المرور: {password}<br><br>للدخول إلى حسابك يرجى الضغط على الرابط أدناه:",
                    "en" => "Hello {username} Thank you for Joining our service.<br>it is your credential to login and reset your password. <br><br>Email: {email} , <br> Password: {password}<br><br>Click the link below to login:"
                ],
                'title' => [
                    'en' => "User Credential",
                    'ar' => "بيانات تسجيل الدخول",
                ],
                'type' => 'info',
                'providers' => ["email"],
                'action'=>'system'
            ],
            [
                'name' => 'new account access',
                'key' => 'new_account_access_add',
                'icon' => 'bx bx-circle',
                'url' => url('/admin/login'),
                'body' => [
                    "ar" => "مرحباً {username}<br><br>لقد لاحظنا إضافة وصول جديد إلى حسابك الحالي في خدمتنا.<br><br>يمكنك متابعة استخدام بيانات الدخول الحالية الخاصة بك لتسجيل الدخول<br><br>انقر على الرابط أدناه لتسجيل الدخول:" ,
                    "en" => "Hello {username}<br><br>We noticed a new access has been added to your existing account in our service.<br><br>You can continue using your existing credentials to login<br><br>Click the link below to login:"
                ],
                'title' => [
                    'en' => "New Access Added to Kera Platform",
                    'ar' => "تم إضافة وصول جديد لحسابك في منصة كراء",
                ],
                'type' => 'info',
                'providers' => ["email"],
                'action'=>'system'
            ],
            [
                'name' => 'Verify Email',
                'key' => 'verify_account_email',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'body' => [
                    "ar" => 'مرحباً {username}، نشكرك على التسجيل في خدمتنا. لإكمال عملية التحقق، يرجى استخدام رمز التحقق (OTP) التالي:
                            <br>
                            <br>
                            رمز التحقق: {confirmation_code}
                            <br>
                            يرجى إدخال هذا الرمز في الحقل المخصص لتأكيد حسابك.
                            شكراً لك',
                    "en" => 'Hello {username}, Thank you for registering with our service. To complete the verification process, please use the following OTP (One-Time Password) code:
                    <br><br>
                    OTP Code: {confirmation_code}
                    <br>
                    Please enter this code in the provided field to verify your account.
                    Thank you',
                ],
                'title' => [
                    'en' => "Verify Email",
                    'ar' => "تأكيد البريد الإلكتروني",
                ],
                'type' => 'info',
                'providers' => ["fcm-api", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Reset Password',
                'key' => 'users_password_reset',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'body' => [
                    "ar" => "مرحباً {username}، اضغط على الرابط أدناه لإعادة تعيين كلمة المرور:<br><br><a href='{base_url}/users-manager/users/resetpass/{login_token}'>إعادة تعيين كلمة المرور</a>",
                    "en" => "Hello {username}, Click the link below to reset your password:
                        <br><br>
                        <a href='{base_url}/users-manager/users/resetpass/{login_token}'>Reset Password</a>"
                ],
                'title' => [
                    'en' => 'Reset Password',
                    'ar' => "إعادة تعيين كلمة المرور",
                ],
                'type' => 'info',
                'providers' => ["fcm-api", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Change Email Old',
                'key' => 'users_change_email',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'body' => [
                    "ar" => "نود إعلامك بأنه تم تغيير البريد الإلكتروني بنجاح، ويمكنك استخدام البريد الإلكتروني الجديد لتسجيل الدخول الآن",
                    "en" => "We Want to Inform you that this email changed successfully and you can use new email to login now"
                ],
                'title' => [
                    'en' => 'Change Email',
                    'ar' => "تغيير البريد الإلكتروني",
                ],
                'type' => 'info',
                'providers' => ["fcm-api", "email"],
                'action' => 'system'
            ],
            //tickets
            [
                'name' => 'New Ticket',
                'key' => 'new_ticket',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'body' => ["ar" => "لديك تذكره جديده من {account} ",
                    "en" => "You Have A New Ticket from {account}"],
                'title' => [
                    'en' => 'New Ticket',
                    'ar' => 'لديك تذكره جديده ',
                ],
                'type' => 'info',
                'providers' => ["fcm-web", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'New Ticket Replay By Admin',
                'key' => 'new_ticket_replay_admin',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'body' => ["ar" => "قام {account}بالرد على تذكره رقم {ticket} ",
                    "en" => "{account} Replayed On {ticket}"],
                'title' => [
                    'en' => 'New Ticket Replay',
                    'ar' => 'لديك  رد على تذكره  ',
                ],
                'type' => 'info',
                'providers' => ["fcm-api"],
                'action'=>'system'
            ],
            [
                'name' => 'New Ticket Replay By Customer',
                'key' => 'new_ticket_replay_customer',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'body' => ["ar" => "قام {account} بالرد على تذكره رقم {ticket}",
                    "en" => "{account} Replayed On {ticket}"],
                'title' => [
                    'en' => 'New Ticket Replay',
                    'ar' => 'لديك  رد على تذكره  ',
                ],
                'type' => 'info',
                'providers' => ["fcm-web", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Assign Ticket',
                'key' => 'ticket_support_assignee',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'body' => [
                    "ar" => "تم تعيين تذكرة دعم جديدة لك بأولوية {priority}، يرجى مراجعة التذكرة والرد على العميل في أقرب وقت",
                    "en" => "You have been assigned a new support ticket. with {priority} Priority,Please review the ticket and respond to the customer promptly."
                ],
                'title' => [
                    'en' => 'New Ticket Assigned: # {uuid}',
                    'ar' => 'تم تعيين تذكرة جديدة: # {uuid}',
                ],
                'providers' => ["fcm-web", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Ticket Closed',
                'key' => 'ticket_closed',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'body' => [
                    "ar" => "تم اغلاق التذكرة الخاصة بك بنجاح رقم # {uuid}",
                    "en" => "your ticket number # {uuid} closed successfully"
                ],
                'title' => [
                    'en' => 'Ticket Closed: # {uuid}',
                    'ar' => 'تم اغلاق تذكرة رقم: # {uuid}',
                ],
                'providers' => ["fcm-api"],
                'action'=>'system'
            ],
            [
                'name' => 'New Lease',
                'key' => 'new_lease_created',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'body' => [
                    "ar" => "تم اضافة عقد جديد علي العقار {property_name}",
                    "en" => "New lease created on {property_name}."
                ],
                'title' => [
                    'en' => 'New Lease',
                    'ar' => 'عقد جديد',
                ],
                'providers' => ["fcm-api", "fcm-web", "email"],
                'action'=>'system'
            ],
            //maintenance
            [
                'name' => 'New Maintenance Request',
                'key' => 'new_maintenance_req',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'New Maintenance Request',
                    'ar' => 'لديك طلب صيانة جديد',
                ],
                'body' => [
                    "en" => "There are new Maintenance Request for unit {title} on {expected_date}",
                    "ar" => "لديك طلب صيانة جديد علي الوحدة {title} في تاريخ {expected_date}"
                ],
                'providers' => ["fcm-api", "fcm-web", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Modify Maintenance Request',
                'key' => 'modify_maintenance_req',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Maintenance Request',
                    'ar' => 'طلب صيانة',
                ],
                'body' => [
                    "en" => "Your maintenance request status has been updated to {status} at {expected_date}",
                    "ar" => "تم تعديل حالة طلب الصيانة الخاص بك الي {status} في تاريخ {expected_date}"
                ],
                'providers' => ["fcm-api"],
                'action'=>'system'
            ],
            //lease
            [
                'name' => 'New Termination Request',
                'key' => 'termination_request_add',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'New Termination Request Created',
                    'ar' => 'تم إنشاء طلب فسخ عقد جديد',
                ],
                'body' => [
                    "en" => "There are new Termination Request Created . Explore and Make Decision",
                    "ar" => "تم إنشاء طلب فسخ عقد جديد، يرجى الاطلاع واتخاذ القرار"
                ],
                'providers' => ["fcm-api", "fcm-web"],
                'action'=>'system'
            ],
            [
                'name' => 'New Close Request',
                'key' => 'close_request_add',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'New Close Request Created',
                    'ar' => 'تم إنشاء طلب إغلاق جديد',
                ],
                'body' => [
                    "en" => "There are new Close Request Created . Explore and Make Decision",
                    "ar" => "يوجد طلب إغلاق جديد، يرجى الاطلاع واتخاذ القرار"
                ],
                'providers' => ["fcm-api", "fcm-web"],
                'action'=>'system'
            ],
            //payments
            [
                'name' => 'Successful Payment',
                'key' => 'payment_success',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Your Payment Process Is Submitted Successfully',
                    'ar' => 'تمت عملية الدفع الخاصة بك بنجاح',
                ],
                'body' => [
                    "en" => "Your Payment Process Is Submitted Successfully",
                    "ar" => "تم تقديم عملية الدفع الخاصة بك بنجاح"
                ],
                'providers' => ["fcm-api", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Failed Payment',
                'key' => 'payment_failed',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Your Payment Process Is Failed',
                    'ar' => 'تعذر إتمام عملية الدفع الخاصة بك',
                ],
                'body' => [
                    "en" => "Your Payment Process Is Failed Please Try Again",
                    "ar" => "فشلت عملية الدفع الخاصة بك، يرجى المحاولة مرة أخرى"
                ],
                'providers' => ["fcm-api", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Kera Receipts',
                'key' => 'send_invoice_notification',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Kera Receipts',
                    'ar' => 'إيصالات كراء',
                ],
                'body' => [
                    "en" => 'Thanks for paying {username}',
                    "ar" => 'شكرا لك علي الدفع {username}'
                ],
                'providers' => ["fcm-api"],
                'action'=>'system'
            ],
            [
                'name' => 'New installment paid',
                'key' => 'new_payment_instalment',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'New installment paid # {ref_number}',
                    'ar' => 'تم دفع قسط جديد # {ref_number}',
                ],
                'body' => [
                    "en" => 'There are new installment paid , you can show the receipt from this <a href="{url}">Download</a>',
                    "ar" => 'تم دفع قسط جديد يمكنك عرض الايصال من خلال <a href="{url}">التحميل</a>'
                ],
                'providers' => ["fcm-api", "fcm-web"],
                'action'=>'system'
            ],
            [
                'name' => 'New Invoice Issued',
                'key' => 'new_invoice_issued',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'New Invoice Issued',
                    'ar' => 'تم إصدار فاتورة جديدة',
                ],
                'body' => [
                    "en" => 'There are new Invoice Issued , You can explore your invoices',
                    "ar" => 'تم إصدار فاتورة جديدة برجاء الاطلاع علي الفواتير'
                ],
                'providers' => ["fcm-api", "fcm-web" , "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Val Expired',
                'key' => 'val_lisence_expired',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Val Lisence Expired',
                    'ar' => 'تم انتهاء رخصة فال الخاصة بك',
                ],
                'body' => [
                    "en" => "There is val lisence expired try to renew to show your data",
                    "ar" => "تم انتهاء رخصة فال الخاصة بك حاول تجديها لاعادة اظهار الداتاالمتعلقة بها"
                ],
                'providers' => ["email", "fcm-web"],
                'action'=>'system'
            ],
            [
                'name' => 'Payment Paid Susseccfully',
                'key' => 'new_payment',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Payment Paid successfully',
                    'ar' => 'تم دفع القيمة بنجاح',
                ],
                'body' => [
                    "en" => 'Payment You have paid had been confirmed successfully , you can view the invoice update after payment',
                    "ar" => 'تم دفع الدفعة بنجاح يمكنك الاطلاع علي الفاتورة بعد تأكيد الدفع'
                ],
                'providers' => ["fcm-api", "fcm-web" , "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Lease Renewal Reminder',
                'key' => 'lease_renewal_reminder',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Lease Number {lease_id} Renewal',
                    'ar' => 'تذكير بخصوص تجديد العقد رقم{lease_id}',
                ],
                'body' => [
                    "en" => 'we remind you that The lease Number which you are memer in Will be renewed within {days} , which was starting at {start_date} and {end_date} and the new end date will be {new_end_date}',
                    "ar" => 'نذكرك بأن رقم الإيجار الذي أنت عضو فيه سيتم تجديده خلال {days}، والذي بدأ في {start_date} وانتهى في {end_date} وتاريخ الانتهاء الجديد سيكون {new_end_date}',
                ],
                'providers' => ["fcm-api", "fcm-web", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Request Member Approved',
                'key' => 'request_member_approved',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'There is request member approved',
                    'ar' => 'تم الموافقة علي طلبك من قبل احد الاطراف',
                ],
                'body' => [
                    "en" => 'Your request number {request_number} had been approved from one member',
                    "ar" => '{request_number} تم الموافقة علي طلبك من قب احد الأطراف رقم الطلب',
                ],
                'providers' => ["fcm-api", "fcm-web", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Your Request had been approved',
                'key' => 'request_approved',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Your Request Had been approved from all members',
                    'ar' => 'تمت الموافقة علي طلبك من قبل جميع الاطراف',
                ],
                'body' => [
                    "en" => 'your request {request_number} had been approved from all members',
                    "ar" => 'تمت الموافقة على طلبك {request_number} من جميع الأعضاء',
                ],
                'providers' => ["fcm-api", "fcm-web", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Your Request had been rejected',
                'key' => 'request_rejected',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Your Request Had been rejected from all members',
                    'ar' => 'تمت الموافقة علي طلبك من قبل جميع الاطراف',
                ],
                'body' => [
                    "en" => 'your request {request_number} had been rejected',
                    "ar" => 'تم رفض طلبك {request_number}',
                ],
                'providers' => ["fcm-api", "fcm-web", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'Lease Auto Renewal Request',
                'key' => 'lease_auto_renewal_request',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'There Is Lease Has Auto Renewal Request',
                    'ar' => 'لديك طلب تجديد تلقائي لعقد',
                ],
                'body' => [
                    "en" => 'There Is New Auto Renewal Request Belongs To The Lease Number {lease_id} which start at {start_date} and ends in {end_date} and the new End Date will be {newEndDate}',
                    "ar" => 'يوجد طلب تجديد تلقائي جديد يخص العقد رقم {lease_id} الذي يبدأ في {start_date} وينتهي في {end_date} وتاريخ الانتهاء الجديد سيكون {newEndDate}',
                ],
                'providers' => ["fcm-api", "fcm-web", "email"],
                'action'=>'system'
            ],

            [
                'name' => 'Lease Auto Renewal Cancellation',
                'key' => 'lease_auto_renewal_cancel',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'There Is Lease Auto Renewal Cancelled',
                    'ar' => 'تم الغاء التجديد التلقائي للعقد',
                ],
                'body' => [
                    "en" => 'Auto Renewal Belongs To The Lease Number {lease_id} had been cancelled',
                    "ar" => 'التجديد التلقائي الخاص بعقد الإيجار رقم {lease_id} تم إلغاؤه',
                ],
                'providers' => ["fcm-api", "fcm-web", "email"],
                'action'=>'system'
            ],
            [
                'name' => 'New Assignee',
                'key' => 'broker_new_assigned',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'New Broker Assignee',
                    'ar' => 'تعيين بروكر جديد',
                ],
                'body' => [
                    "en" => "You have been assigned as a broker in a new property explore properties",
                    "ar" => "تم تعيينك كوسيط ف وحدة جديدة تفقد العقاؤات لاتخاد الاجراءات اللازمة من جانبك"
                ],
                'providers' => ["email", "fcm-web"],
                'action'=>'system'
            ],
            [
                'name' => 'Removed Assignee',
                'key' => 'broker_remove',
                'icon' => 'bx bx-circle',
                'url' => '#',
                'title' => [
                    'en' => 'Broker Assignee Removed',
                    'ar' => 'الغاء تعيين البروكر',
                ],
                'body' => [
                    "en" => "You are been removed from a property as broker  if this by mistake contact your company owner or kera customer support",
                    "ar" => "تم الغاء تعيينك من احد العقارات كوسيط اذا كان ذلك عن طريق الخطأ تواصل مع مؤسس الشركة العقارية او مع الدعم الفني لكراء"
                ],
                'providers' => ["email", "fcm-web"],
                'action'=>'system'
            ],
        ];
        foreach ($data as $item) {
            $ifex = NotificationsTemplate::where('key', $item['key'])->first();
            if (!$ifex) {
                NotificationsTemplate::create($item);
            } else {
                $ifex->update($item);
            }
        }
    }
}
