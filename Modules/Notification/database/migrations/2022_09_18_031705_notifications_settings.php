<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

class NotificationsSettings extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('notifications.notifications_allow', false);
        $this->migrator->add('notifications.fcm_apiKey', 'AIzaSyArb0hmNaHx_Y90CwyZIAds7_6enlDSats');
        $this->migrator->add('notifications.fcm_authDomain', 'keraa-ebca0.firebaseapp.com');
        $this->migrator->add('notifications.fcm_projectId', 'keraa-ebca0');
        $this->migrator->add('notifications.fcm_storageBucket', 'keraa-ebca0.appspot.com');
        $this->migrator->add('notifications.fcm_messagingSenderId', '894133070174');
        $this->migrator->add('notifications.fcm_appId', '1:894133070174:web:e515c2102b543b3db947cf');
        $this->migrator->add('notifications.fcm_measurementId', 'G-J8Q69PYXFD');
    }
}
