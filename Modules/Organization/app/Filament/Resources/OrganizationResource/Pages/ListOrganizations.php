<?php

namespace Modules\Organization\app\Filament\Resources\OrganizationResource\Pages;

use Modules\Organization\app\Filament\Resources\OrganizationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOrganizations extends ListRecords
{
    protected static string $resource = OrganizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Organization')),
        ];
    }
}
