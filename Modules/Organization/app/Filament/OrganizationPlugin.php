<?php

namespace Modules\Organization\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Organization\app\Filament\Resources\OrganizationResource;

class OrganizationPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return __('Organization');
    }

    public function getId(): string
    {
        return __('Organization');
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }

    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                OrganizationResource::class,
            ]);
    }
}
