<?php

namespace Modules\Organization\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;


class OrganizationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            'name' => $this->name,
            "unified_number" => $this->unified_number,
            "ownership_document_number" => $this->ownership_document_number,
        ];
        return $data;

    }
}
