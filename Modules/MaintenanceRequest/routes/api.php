<?php

use Illuminate\Support\Facades\Route;
use Modules\MaintenanceRequest\app\Http\Controllers\Api\MaintenanceRequestController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(function () {
    Route::get('maintenance_requests/filter', [MaintenanceRequestController::class, 'filter']);
    Route::apiResource('maintenance_requests', MaintenanceRequestController::class);
});
