<?php

namespace Modules\MaintenanceRequest\app\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;
use Khaleds\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;
use Illuminate\Database\Eloquent\Builder;


class MaintenanceRequestRepository extends RepositoriesAbstract
{
    public function __construct(MaintenanceRequest $model)
    {
        parent::__construct($model);
    }

    public function filterAllBy(array $condition = [], array $with = [], array $select = ['*']): LengthAwarePaginator
    {
        $query = $this->model::query()->with($with);
        $query = $this->applyFilters($query, $condition);
        return $query->paginate(request()->limit ?? 10);
    }

    private function applyFilters(Builder $query, array $filters)
    {
        if (!empty($filters['search']['service_id'])) {
            $query->where('service_id', $filters['search']['service_id']);
        }

        if (!empty($filters['search']['usability_id'])) {
            $query->whereHas('unit', function ($q) use ($filters) {
                $q->where('usability_id', $filters['search']['usability_id']);
            });
        }

        if (!empty($filters['search']['property_type_id'])) {
            $query->whereHas('unit', function ($q) use ($filters) {
                $q->where('property_type_id', $filters['search']['property_type_id']);
            });
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['from'])) {
            $query->whereDate('expected_date', '>=', $filters['from']);
        }

        if (!empty($filters['to'])) {
            $query->whereDate('expected_date', '<=', $filters['to']);
        }

        $query->where('account_id', auth()->id());
        $query->where('account_type', auth()->user()::class);

        return $query;
    }
}



