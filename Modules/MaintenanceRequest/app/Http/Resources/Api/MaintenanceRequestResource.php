<?php

namespace Modules\MaintenanceRequest\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Property\app\Resources\Api\PropertyResource;
use Modules\Property\app\Resources\Api\UnitResource;
use Modules\Service\app\Http\Resources\Api\ServicesResource;

class MaintenanceRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id ?? null,
            "unit" => $this->whenLoaded('unit', function (){
                return UnitResource::make($this->unit);
            }),
            "account_type" => $this->account_type,
            "account_id" => $this->account_id,
            "description" => $this->description,
            "status" => $this->status,
            "reason" => $this->reason,
            "service" => $this->whenLoaded('service', function (){
                return ServicesResource::make($this->service);
            }),
            "expected_date" => $this->expected_date,
            "service_time" => $this->service_time,
            "lease_id" => $this->lease_id,
            "images" => collect($this->getMediaImages('maintenance_requests'))->pluck('original_url')->toArray(),
            "created_at" => date($this->created_at),
        ];

        return $data;

    }
}
