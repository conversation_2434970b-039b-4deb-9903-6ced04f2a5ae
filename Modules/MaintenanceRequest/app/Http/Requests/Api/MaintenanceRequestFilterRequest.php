<?php

namespace Modules\MaintenanceRequest\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;


class MaintenanceRequestFilterRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'search.service_id' => 'sometimes|integer',
            'search.usability_id' => 'sometimes|integer',
            'search.property_type_id' => 'sometimes|integer',
            'status' => 'sometimes|string',
            'from' => 'sometimes|date',
            'to' => 'sometimes|date',
        ];
        return $rules;
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated();
        $validated['account_id'] = auth()->id();
        $validated['account_type'] =auth()->user()::class;
        return $validated;
    }
}
