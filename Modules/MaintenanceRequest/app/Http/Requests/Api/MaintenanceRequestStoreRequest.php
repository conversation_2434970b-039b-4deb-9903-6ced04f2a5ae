<?php

namespace Modules\MaintenanceRequest\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;


class MaintenanceRequestStoreRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'unit_id'   => 'required|exists:properties,id',
            'description'   => 'nullable|string',
            'reason'        => 'nullable|string|max:255',
            'expected_date' => 'required|date',
            'service_id'    => 'required',
            'service_time'  => 'required|date_format:H:i:s',
            'lease_id'      => 'required',
            'images' => ['sometimes', 'array', 'min:1', 'max:5'],
            'images.*'      => [
                'sometimes',
                'image',
                'max:10000',
                'mimes:jpeg,png,jpg,gif,webp',  // Added webp
                'mimetypes:image/*', // Added explicit mimetypes
            ]
        ];
        return $rules;
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated();
        $validated['account_id'] = auth()->id();
        $validated['account_type'] =auth()->user()::class;
        return $validated;
    }
}
