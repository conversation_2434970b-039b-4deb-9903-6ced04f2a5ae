<?php

namespace Modules\MaintenanceRequest\app\Http\Controllers\Api;

use Illuminate\Support\Facades\Auth;
use Khaleds\Shared\Helpers\ApiResponse;
use Modules\MaintenanceRequest\app\Http\Requests\Api\MaintenanceRequestFilterRequest;
use Modules\MaintenanceRequest\app\Http\Requests\Api\MaintenanceRequestUpdateRequest;
use Modules\MaintenanceRequest\app\Http\Resources\Api\MaintenanceRequestResource;
use Modules\MaintenanceRequest\app\Http\Requests\Api\MaintenanceRequestStoreRequest;
use Modules\MaintenanceRequest\app\Services\MaintenanceRequestService;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Illuminate\Http\Request;
use Modules\MaintenanceRequest\Enums\maintenanceRequestAccountTypeEnum;
use Modules\MaintenanceRequest\Enums\maintenanceRequestStatusEnum;


class MaintenanceRequestController extends ControllerAbstract
{
    protected string $jsonResourceClass = MaintenanceRequestResource::class;
    protected string $storeRequestClass = MaintenanceRequestStoreRequest::class;
    protected string $updateRequestClass = MaintenanceRequestUpdateRequest::class;

    public function __construct(MaintenanceRequestService $service)
    {
        parent::__construct($service);
    }

    public function index(Request $request)
    {
        if ($request->has('status')) {
            $this->filter['status'] = $request->get('status');
        }
        $this->with = ['service', 'unit', 'lease', 'lease.brokers', 'unit.usability', 'unit.property_type'];
        return parent::index($request);
    }

    public function show(int $id)
    {
        $this->with = ['service', 'unit', 'unit.usability', 'unit.property_type'];
        return parent::show($id);
    }

    public function filter(MaintenanceRequestFilterRequest $request)
    {
        $conditions = $request->validated();
        $this->with = ['service', 'unit', 'unit.usability', 'unit.property_type'];
        return ApiResponse::data($this->jsonResourceClass::collection($this->service->filter($conditions, $this->with, $this->select)));
    }

    public function update(Request $request, $id)
    {
        $this->filter['status'] = maintenanceRequestStatusEnum::PENDING;
        return parent::update($request, $id);
    }

    public function destroy($id)
    {
        $this->filter['status'] = maintenanceRequestStatusEnum::PENDING;
        return parent::destroy($id);
    }
}
