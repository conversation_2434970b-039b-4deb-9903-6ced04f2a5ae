<?php

namespace Modules\MaintenanceRequest\app\Models;

use App\Shared\HasRelationshipChecks;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;
use Modules\Account\app\Models\Account;
use Modules\Account\Enums\AccountRolesEnum;
use Modules\Account\Helpers\AccountHelper;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\app\Models\InvoiceItem;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseMember;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Property\app\Models\Property;
use Modules\Service\app\Models\Service;
use Modules\Tenancy\Traits\BelongsToTenancy;

class MaintenanceRequest extends BaseModel
{
    use SoftDeletes;
    use BelongsToTenancy;
    use HasRelationshipChecks;


    protected $translatable=['name'];

    protected $relationsList = ['invoices'];
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = [
        'unit_id','account_type','account_id','description','status','reason','visit_time','expected_date','service_id','service_time','lease_id'
    ];

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('accountTypeScope', function (Builder $builder) {
            if (request()->is('api/*')) {
                $account = auth()->user();
                if ($account) {
                    $current_role = AccountHelper::CurrentRole(); //current auth account role
                    if ($current_role) {
                        //todo refactor this condition
                        if ($current_role == AccountRolesEnum::OWNER){
                            $current_role = LeaseMemberTypesEnum::LESSOR;
                        } elseif ($current_role == AccountRolesEnum::OWNER_REPRESENTER) {
                            $current_role = LeaseMemberTypesEnum::LESSOR_REPRESENTER;
                        }

                        //if role is tenant representer
                        if ($current_role == AccountRolesEnum::TENANT_REPRESENTER) {
                            $builder->where('account_type', get_class($account))
                                ->where('account_id', $account->id)
                                ->whereHas('lease', function($query) use ($account, $current_role) {
                                    $query->whereHas('leaseMembers', function($subQuery) use ($account, $current_role) {
                                        $subQuery->where('member_role', $current_role)
                                            ->where('member_id', $account->id);
                                    });
                                });
                        } elseif ($current_role == AccountRolesEnum::TENANT) {
                            //get data of tenant account with his representer data
                            // Get account's lease IDs
                            $accountLeaseIds = LeaseMember::query()
                                ->where('member_id', $account->id)
                                ->where('member_role', $current_role)
                                ->pluck('lease_id')->toArray();

                            $requests_ids = [];
                            foreach ($accountLeaseIds as $accountLeaseId) {
                                $accountsInLease = LeaseMember::query()
                                    ->where('lease_id', $accountLeaseId)
                                    ->whereIn('member_role', [AccountRolesEnum::TENANT, AccountRolesEnum::TENANT_REPRESENTER])
                                    ->pluck('member_id')->toArray();

                                $roles_requests_ids = self::withoutGlobalScope('accountTypeScope') //to avoid infinite loop
                                    ->where('lease_id', $accountLeaseId)
                                    ->whereIn('account_id', $accountsInLease)
                                    ->where('account_type', Account::class)
                                    ->pluck('id')->toArray();
                                $requests_ids = array_merge($requests_ids, $roles_requests_ids);
                            }

                            $builder->whereIn('id', array_unique($requests_ids));
                        } else {
                            //get all data for owner or owner representer
                            // Get account's lease IDs
                            $accountLeaseIds = LeaseMember::query()
                                ->where('member_id', $account->id)
                                ->where('member_role', $current_role)
                                ->pluck('lease_id')->toArray();

                            $requests_ids = [];
                            foreach ($accountLeaseIds as $accountLeaseId) {
                                $roles_requests_ids = self::withoutGlobalScope('accountTypeScope') //to avoid infinite loop
                                ->where('lease_id', $accountLeaseId)->pluck('id')->toArray();
                                $requests_ids = array_merge($requests_ids, $roles_requests_ids);
                            }

                            $builder->whereIn('id', array_unique($requests_ids));
                        }
                    }
                }
            }
        });
    }
    public function unit()
    {
        return $this->belongsTo(Property::class, 'unit_id')->where('parent_id',  '!=', null);
    }

    public function account()
    {
        return $this->morphTo();
    }

    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    public function lease()
    {
        return $this->belongsTo(Lease::class);
    }

    public function invoices()
    {
        return $this->hasManyThrough(
            Invoice::class,
            InvoiceItem::class,
            'item_id',
            'id',
            'id',
            'invoice_id'
        )
        ->where('item_type', MaintenanceRequest::class);
    }
}
