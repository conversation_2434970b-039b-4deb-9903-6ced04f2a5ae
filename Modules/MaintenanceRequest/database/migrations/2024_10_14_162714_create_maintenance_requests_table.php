<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\MaintenanceRequest\Enums\maintenanceRequestStatusEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('maintenance_requests', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('property_id')->nullable()->default(null);
            $table->foreign('property_id')->references('id')->on('properties')->onDelete('cascade')->onUpdate('cascade');            
            $table->morphs(name: 'account');
            $table->text('description')->nullable(); 
            $table->enum('status', maintenanceRequestStatusEnum::getMaintenanceRequestStatusValues())->default(maintenanceRequestStatusEnum::PENDING);
            $table->string('reason', 255)->nullable();
            $table->dateTime('visit_time')->nullable();
            $table->date('expected_date')->nullable();
            $table->unsignedBigInteger('service_id')->nullable()->default(null);
            $table->time('service_time')->nullable();
            $table->unsignedBigInteger('lease_id')->nullable()->default(null);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('maintenance_requests');
    }
};
