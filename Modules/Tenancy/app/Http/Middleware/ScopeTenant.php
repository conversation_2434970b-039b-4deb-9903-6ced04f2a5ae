<?php

namespace Modules\Tenancy\app\Http\Middleware;

use App\Enums\RoleEnum;
use App\Models\Document;
use App\Models\User;
use App\Models\Val;
use App\Services\UserSessionService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Schema;
use Khaleds\Shared\Models\BaseModel;
use Lab404\Impersonate\Services\ImpersonateManager;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;
use Modules\Property\app\Models\Property;
use Modules\Tenancy\app\Models\scopes\TenantAsACompanyScope;
use Modules\Tenancy\app\Models\scopes\TenantCreationScope;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class ScopeTenant
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        //todo single or multibale db beteer for roles and permission solutions
//        $models = config('tenancy.models');
//
//
//        foreach ($models as $model){
//            $model::addGlobalScope(new TenantAsACompanyScope);
//        }
//
//        return $next($request);
        try {

//            $response = $next($request);
//            dd(auth()->user());
            // Check for logout request
//            dd($request->route());
//            if ($request->route('logout')) {
//                dd('ddd');
////                if($request->session()->has('userTenancy')){
////                    session()->forget('userTenancy');
////                    cache()->clear();
////                }
////                return $next($request);
//            }
//
            if ($request->path() == 'filament-impersonate/leave') {
                session()->forget('userTenancy');
                cache()->clear();
                return $next($request);
            }

            if (app(ImpersonateManager::class)->isImpersonating() && request()->routeIs("filament-impersonate.leave"))
                return;
            if(auth()->check() && !auth()->user()->hasRole(RoleEnum::ADMIN)){
                $currentUser = auth()->user();

                // Validate session consistency
                if (!UserSessionService::validateUserSession($currentUser)) {
                    // Clear inconsistent session data
                    session()->forget('userTenancy');
                    cache()->clear();

                    // Force logout to prevent authentication issues
                    auth()->logout();
                    session()->flush();

                    return redirect()->route('filament.admin.auth.login')
                        ->with('error', __('Your company assignment has been changed. Please log in again.'));
                }

                if (!$request->session()->has('userTenancy')) {
                    if(!is_null($currentUser->company?->id) || !is_null($currentUser->company_id)){
                        session()->put(['userTenancy' => $currentUser]);
                    }
                }
            }


//            // Handle different response types
//            if ($response instanceof \Illuminate\Http\RedirectResponse) {
//                return $response;
//            }
//
//            if ($response instanceof \Illuminate\Http\JsonResponse) {
//                return $response;
//            }
//
//            if ($response instanceof \Symfony\Component\HttpFoundation\BinaryFileResponse) {
//                return $response;
//            }

            return $next($request);

        } catch (\Exception $e) {
            // Log the error if needed
            info('Tenancy Middleware Error: ' . $e->getMessage());

            return redirect()->back();
        }
    }

}
